import React, { useState, useMemo, useEffect, useCallback } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DialogContentText from '@mui/material/DialogContentText';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Paper from '@mui/material/Paper';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Grid from '@mui/material/Grid';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ToggleButton from '@mui/material/ToggleButton';
import LinearProgress from '@mui/material/LinearProgress';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Badge from '@mui/material/Badge';
import {
  CardGiftcard as CardGiftcardIcon,
  ReceiptLong as ReceiptLongIcon,
  Add as AddIcon,
  CalendarToday as CalendarTodayIcon,
  CheckCircle as CheckCircleIcon,
  Close as CloseIcon,
  HourglassEmpty as HourglassEmptyIcon,
  AccessTime as AccessTimeIcon,
  History as HistoryIcon,
  Delete as DeleteIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Check as CheckIcon,
  Search as SearchIcon,
  AssignmentTurnedIn as AssignmentTurnedInIcon,
  Pets as PetsIcon,
  Style as StyleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { CustomerPackage, PackageUsageHistory } from '../../types/packages';
import { Service } from '../../types/sales';
import { formatDate } from '../../types/sales';
import { PackageHistoryDialog } from './PackageHistoryDialog';
import { ResolveNoShowDialog } from './ResolveNoShowDialog';
import { useCustomerPackages } from '../../hooks/useCustomerPackages';
import { BuyPackageDialog } from '../Packages/BuyPackageDialog';
import { usePackages } from '../../hooks/usePackages';
import { usePets } from '../../hooks/usePets';
import { useServices } from '../../hooks/useServices';

interface CustomerPackagesDialogProps {
  open: boolean;
  onClose: () => void;
  customerName: string;
  customerId: number;
}

interface CaptchaProps {
  onComplete: () => void;
}

// Componente de Captcha com ícone de animal que faz backflip
const PetBackflipCaptcha: React.FC<CaptchaProps> = ({ onComplete }) => {
  const [rotation, setRotation] = useState(0);
  const [isSolved, setIsSolved] = useState(false);
  const [petIcon, setPetIcon] = useState('🐕');
  
  // Escolhe um ícone de animal aleatório quando o componente é montado
  useEffect(() => {
    const petIcons = ['🐕', '🐈', '🐇', '🐹', '🦜', '🐠', '🦎'];
    const randomIndex = Math.floor(Math.random() * petIcons.length);
    setPetIcon(petIcons[randomIndex]);
  }, []);

  // Realiza a rotação para trás (360 graus)
  const rotateBackward = () => {
    if (isSolved) return;
    
    setRotation(prev => {
      const newRotation = prev + 90; // Rotacionar para a direita (trás do animal)
      // Verifica se o animal fez um backflip completo (360 graus)
      if (newRotation >= 360) {
        setIsSolved(true);
        onComplete();
        return 360; // Mantém na rotação final
      }
      return newRotation;
    });
  };

  // Realiza a rotação para frente
  const rotateForward = () => {
    if (isSolved) return;
    
    setRotation(prev => {
      const newRotation = prev - 90; // Rotacionar para a esquerda (frente do animal)
      return newRotation;
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 2 }}>
      <Typography variant="subtitle1" align="center" gutterBottom>
        Para confirmar a exclusão, faça o animal dar um backflip completo
      </Typography>
      
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2, gap: 2 }}>
        <IconButton 
          onClick={rotateForward}
          color="primary"
          disabled={isSolved}
        >
          <ArrowUpwardIcon />
        </IconButton>
        
        <Box 
          sx={{ 
            fontSize: '3rem', 
            transition: 'transform 0.5s ease',
            transform: `rotate(${rotation}deg)`,
            width: 70,
            height: 70,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: isSolved ? '2px solid #4caf50' : '1px dashed #ccc',
            borderRadius: '50%',
            backgroundColor: isSolved ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
          }}
        >
          {petIcon}
        </Box>
        
        <IconButton 
          onClick={rotateBackward}
          color="primary"
          disabled={isSolved}
          sx={{ 
            animation: isSolved ? 'none' : 'pulse 1.5s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 0.6 },
              '50%': { opacity: 1 },
              '100%': { opacity: 0.6 }
            }
          }}
        >
          <ArrowDownwardIcon />
        </IconButton>
      </Box>
      
      {isSolved && (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
          <CheckIcon sx={{ mr: 1 }} />
          <Typography variant="body2">Backflip completo! Agora você pode excluir o pacote.</Typography>
        </Box>
      )}
    </Box>
  );
};

export const CustomerPackagesDialog: React.FC<CustomerPackagesDialogProps> = ({
  open,
  onClose,
  customerName,
  customerId
}) => {
  const {
    getCustomerPackages,
    activateCustomerPackage,
    assignPackageToCustomer,
    getPackageUsageHistory,
    cancelCustomerPackage,
    loading,
    error
  } = useCustomerPackages();
  const { packages, loading: packagesLoading, error: packagesError } = usePackages();
  const { getPetsByCustomerId, loading: petsLoading, error: petsError } = usePets();
  const { services } = useServices();

  const [customerPackages, setCustomerPackages] = useState<CustomerPackage[]>([]);
  const [loadingLocal, setLoadingLocal] = useState<boolean>(false);
  const [buyDialogOpen, setBuyDialogOpen] = useState(false);
  const [customerPets, setCustomerPets] = useState<any[]>([]);
  const [buyError, setBuyError] = useState<string | null>(null);

  // Added for Big Pappa: State for count of packages in 'Pausados' filter category
  const [pausadosCount, setPausadosCount] = useState(0);

  const [searchTerm, setSearchTerm] = useState<string>('');
  
  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [packageToDelete, setPackageToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [captchaSolved, setCaptchaSolved] = useState<boolean>(false);

  // State for ResolveNoShowDialog
  const [resolveDialogOpen, setResolveDialogOpen] = useState<boolean>(false);
  const [packageToResolve, setPackageToResolve] = useState<CustomerPackage | null>(null);

  const [selectedPackage, setSelectedPackage] = useState<CustomerPackage | null>(null);

  const fetchPackages = useCallback(() => {
    if (open && customerId) {
      setLoadingLocal(true);
      getCustomerPackages(customerId)
        .then(setCustomerPackages)
        .finally(() => setLoadingLocal(false));
    }
  }, [open, customerId, getCustomerPackages]);

  useEffect(() => {
    fetchPackages();
    if (open && customerId) {
        getPetsByCustomerId(customerId).then(setCustomerPets);
    }
  }, [open, customerId, fetchPackages, getPetsByCustomerId]);

  // Add filter state
  const [filterValue, setFilterValue] = useState<string>('todos');
  // History dialog state
  const [historyDialogOpen, setHistoryDialogOpen] = useState<boolean>(false);

  // Helper function to find service names by IDs
  const getServiceNames = (serviceIds: number[]): string[] => {
    return serviceIds
      .map(id => {
        const service = services.find((s: typeof services[0]) => s.id === id);
        return service ? service.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  // Calculate remaining days until expiry
  const getRemainingDays = (expiryDate: string | Date): number => {
    const expiry = new Date(expiryDate);
    const today = new Date();
    const diffTime = expiry.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Generate unique identifier for packages with the same name
  const generatePackageIdentifier = (pkg: CustomerPackage): string => {
    const purchaseDate = new Date(pkg.purchase_date);
    const month = purchaseDate.toLocaleString('pt-BR', { month: 'short' });
    const year = purchaseDate.getFullYear();
    return `${month}/${year}`;
  };

  // Filter and sort packages based on the selected filter and default sorting
  const filteredPackages = useMemo(() => {
    // First sort the packages: newest first, then oldest, then inactive
    const sortedPackages = [...customerPackages].sort((a, b) => {
      // If one is active and the other isn't, active comes first
      if (a.status !== b.status) {
        return a.status === 'active' ? -1 : 1;
      }
      
      // If both are active or both are inactive, sort by purchase date (newest first)
      return new Date(b.purchase_date).getTime() - new Date(a.purchase_date).getTime();
    });

    // Then filter based on the selected filter
    let filtered = sortedPackages;
    switch (filterValue) {
      case 'ativos':
        filtered = filtered.filter(pkg => pkg.status === 'active');
        break;
      case 'pausados':
        filtered = filtered.filter(pkg => 
          pkg.status === 'on_hold_no_show' || 
          pkg.status === 'on_hold_customer_request' || 
          pkg.status === 'pending_activation'
        );
        break;
      case 'concluidos':
        filtered = filtered.filter(pkg => pkg.status === 'completed');
        break;
      case 'expirados':
        filtered = filtered.filter(pkg => pkg.status === 'expired');
        break;
      case 'todos':
        // 'todos' case needs no specific filtering here, as it uses the full sorted list
        break;
    }

    // Apply search filter if search term exists
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(pkg => {
        // Obter o nome do mês da data de compra em português
        const purchaseDate = new Date(pkg.purchase_date);
        const monthFull = purchaseDate.toLocaleString('pt-BR', { month: 'long' }).toLowerCase();
        const monthShort = purchaseDate.toLocaleString('pt-BR', { month: 'short' }).toLowerCase().replace('.', '');
        
        // Verificar se o termo corresponde a algum dos critérios de busca
        return (pkg.package?.name || '').toLowerCase().includes(term) ||
          pkg.id.toString().includes(term) ||
          (pkg.package?.description || '').toLowerCase().includes(term) ||
          monthFull.includes(term) ||
          monthShort.includes(term);
      });
    }

    return filtered;
  }, [customerPackages, filterValue, searchTerm]);

  // Added for Big Pappa: useEffect to calculate pausadosCount
  useEffect(() => {
    const count = customerPackages.filter(pkg => 
      pkg.status === 'on_hold_no_show' || 
      pkg.status === 'on_hold_customer_request' || 
      pkg.status === 'pending_activation'
    ).length;
    setPausadosCount(count);
  }, [customerPackages]);

  const getDisplayPackageStatus = (status: string): { label: string; color: string } => {
    switch (status) {
      case 'active':
        return { label: 'Ativo', color: 'primary.main' };
      case 'on_hold_no_show':
        return { label: 'Pausado (Falta)', color: 'warning.main' };
      case 'completed':
        return { label: 'Concluído', color: 'success.main' };
      case 'expired':
        return { label: 'Expirado', color: 'error.main' };
      case 'cancelled':
        return { label: 'Cancelado', color: 'default' };
      case 'pending_activation':
        return { label: 'Pendente Ativação', color: 'info.main' };
      default:
        return { label: 'Inativo', color: 'text.disabled' };
    }
  };

  const handleFilterChange = (
    event: React.MouseEvent<HTMLElement>,
    newFilterValue: string,
  ) => {
    if (newFilterValue !== null) {
      setFilterValue(newFilterValue);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleOpenHistoryDialog = (pkg: CustomerPackage) => {
    setSelectedPackage(pkg);
    setHistoryDialogOpen(true);
  };

  const handleCloseHistoryDialog = () => {
    setHistoryDialogOpen(false);
    setSelectedPackage(null);
  };

  // Funções para lidar com a exclusão de pacotes
  const handleOpenDeleteDialog = (packageId: number) => {
    setPackageToDelete(packageId);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setPackageToDelete(null);
    setDeleteDialogOpen(false);
    setCaptchaSolved(false);
  };

  const handleOpenResolveDialog = (pkg: CustomerPackage) => {
    setPackageToResolve(pkg);
    setResolveDialogOpen(true);
  };

  const handleCloseResolveDialog = () => {
    setPackageToResolve(null);
    setResolveDialogOpen(false);
  };

  const handlePackageResolved = () => {
    fetchPackages();
  };

  const handleCaptchaComplete = () => {
    setCaptchaSolved(true);
  };

  const handleConfirmDelete = async () => {
    if (packageToDelete) {
      setIsDeleting(true);
      try {
        const result = await cancelCustomerPackage(packageToDelete, 'Cancelado pelo usuário no diálogo de pacotes.');
        if (result && result.status === 'cancelled') {
          fetchPackages();
          handleCloseDeleteDialog();
        } else {
          console.error('Falha ao cancelar o pacote do cliente ou estado não permitido:', result);
        }
      } catch (error) {
        console.error('Erro ao cancelar pacote do cliente:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const handleOpenBuyDialog = () => {
    setBuyError(null);
    if (!customerPets || customerPets.length === 0) {
      setBuyError('O cliente precisa ter pelo menos um pet cadastrado para comprar um pacote.');
      return;
    }
    setBuyDialogOpen(true);
  };

  const handlePurchase = async (
    packageId: number,
    petId: number,
    paymentMethod: string,
    firstAppointmentDate: string,
    firstAppointmentTime: string,
    expiryDate?: string
  ): Promise<void> => {
    setBuyDialogOpen(false);
    setBuyError(null);
    // Helper to parse ISO date and time robustly
    function parseDateTime(dateStr: string, timeStr: string): Date | null {
      if (!dateStr || !timeStr) return null;
      // Ensure both are in ISO format and add seconds for full ISO
      const isoString = `${dateStr}T${timeStr.length === 5 ? timeStr + ':00' : timeStr}`;
      const dateObj = new Date(isoString);
      return isNaN(dateObj.getTime()) ? null : dateObj;
    }
    try {
      const firstAppointmentDateTime = parseDateTime(firstAppointmentDate, firstAppointmentTime);
      if (!firstAppointmentDateTime) {
        setBuyError('Data ou hora inválida. Por favor, selecione novamente.');
        return;
      }
      // 1. Assign the package (creates in pending_activation)
      const customerPackage = await assignPackageToCustomer(
        customerId,
        packageId,
        petId,
        new Date(),
        undefined,
        expiryDate ? new Date(expiryDate) : undefined
      );
      if (!customerPackage) {
        setBuyError('Erro ao atribuir pacote ao cliente.');
        return;
      }
      // 2. Activate the package (schedules first appointment)
      const activated = await activateCustomerPackage(customerPackage.id, firstAppointmentDateTime);
      if (!activated) {
        setBuyError('Erro ao ativar o pacote do cliente.');
        return;
      }
      setLoadingLocal(true);
      getCustomerPackages(customerId)
        .then(setCustomerPackages)
        .finally(() => setLoadingLocal(false));
    } catch (err) {
      setBuyError('Erro ao comprar pacote. Tente novamente.');
    }
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CardGiftcardIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">
                Pacotes de {customerName}
              </Typography>
            </Box>
            <IconButton onClick={onClose} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <Divider />
        
        <DialogContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">{error}</Alert>
          ) : customerPackages.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <CardGiftcardIcon sx={{ fontSize: 60, color: 'text.secondary', opacity: 0.5, mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Nenhum pacote ativo encontrado
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Este cliente não possui pacotes de serviços ativos no momento.
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleOpenBuyDialog}
              >
                Comprar Pacote
              </Button>
              {buyError && <Alert severity="warning" sx={{ mt: 2 }}>{buyError}</Alert>}
            </Box>
          ) : (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="subtitle1">Pacotes</Typography>
                  <ToggleButtonGroup
                    value={filterValue}
                    exclusive
                    onChange={handleFilterChange}
                    size="small"
                    aria-label="Filtro de pacotes"
                  >
                    <ToggleButton value="ativos" aria-label="Pacotes ativos">
                      Ativos
                    </ToggleButton>
                    <ToggleButton value="pausados" aria-label="Pacotes pausados ou pendentes">
                      <Badge badgeContent={pausadosCount} color="error" invisible={pausadosCount === 0} sx={{ mr: pausadosCount > 0 ? 1 : 0 }}>
                        Pausados
                      </Badge>
                    </ToggleButton>
                    <ToggleButton value="concluidos" aria-label="Pacotes concluídos">
                      Concluídos
                    </ToggleButton>
                    <ToggleButton value="expirados" aria-label="Pacotes expirados">
                      Expirados
                    </ToggleButton>
                    <ToggleButton value="todos" aria-label="Todos os pacotes">
                      Todos
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1, maxWidth: 300, mx: 2 }}>
                  <TextField
                    placeholder="Buscar pacotes..."
                    size="small"
                    fullWidth
                    value={searchTerm}
                    onChange={handleSearchChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>
                
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={handleOpenBuyDialog}
                >
                  Comprar Novo Pacote
                </Button>
              </Box>
              
              {filteredPackages.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    Nenhum pacote encontrado para o filtro selecionado.
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  {filteredPackages.map((pkg) => {
                    // Defensive: handle null expiry_date
                    const remainingDays = pkg.expiry_date ? getRemainingDays(pkg.expiry_date) : null;
                    const identifier = generatePackageIdentifier(pkg);
                    // Defensive: handle null package and new field names
                    const totalOccurrences = pkg.package?.total_occurrences ?? 0;
                    const servicesProgressPercentage = totalOccurrences > 0 ? (pkg.remaining_occurrences / totalOccurrences) * 100 : 0;
                    return (
                      <Grid item xs={12} key={pkg.id}>
                        <Paper 
                          elevation={2} 
                          sx={{ 
                            p: 2,
                            borderLeft: '4px solid',
                            borderColor: getDisplayPackageStatus(pkg.status).color,
                            position: 'relative',
                            overflow: 'hidden',
                            opacity: pkg.status === 'active' || pkg.status === 'on_hold_no_show' ? 1 : 0.7
                          }}
                        >
                          {/* Package Name and Remaining Services */}
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                            <Typography variant="h6" color={getDisplayPackageStatus(pkg.status).color} sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                              {pkg.package?.name || 'Pacote'} 
                              <Typography component="span" variant="caption" sx={{ ml: 1, fontWeight: 'normal', color: 'text.secondary' }}>
                                #{pkg.id} {pkg.purchase_date ? formatDate(pkg.purchase_date.toString()).substring(0, 10) : ''}
                              </Typography>
                              {pkg.status !== 'active' && (
                                <Chip 
                                  label={getDisplayPackageStatus(pkg.status).label} 
                                  color={getDisplayPackageStatus(pkg.status).color === 'primary.main' ? 'primary' : 
                                         getDisplayPackageStatus(pkg.status).color === 'warning.main' ? 'warning' :
                                         getDisplayPackageStatus(pkg.status).color === 'success.main' ? 'success' :
                                         getDisplayPackageStatus(pkg.status).color === 'error.main' ? 'error' :
                                         getDisplayPackageStatus(pkg.status).color === 'info.main' ? 'info' :
                                         'default'}
                                  size="small"
                                  sx={{ ml: 1 }}
                                />
                              )}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {pkg.status === 'active' && (
                                <Chip 
                                  size="small" 
                                  label={remainingDays !== null ? `${remainingDays} dias restantes` : 'Sem validade'}
                                  color={remainingDays !== null && remainingDays < 10 ? 'error' : remainingDays !== null && remainingDays < 30 ? 'warning' : 'default'}
                                  icon={<AccessTimeIcon />}
                                  sx={{ mr: 1 }}
                                />
                              )}
                              <Box sx={{ textAlign: 'right' }}>
                                <Chip 
                                  label={`${pkg.remaining_occurrences}/${totalOccurrences} serviços`} 
                                  color={pkg.remaining_occurrences > 0 ? 'success' : 'error'}
                                  size="small"
                                  sx={{ mb: 0.5 }}
                                />
                                <LinearProgress 
                                  variant="determinate" 
                                  value={servicesProgressPercentage} 
                                  sx={{ 
                                    height: 4, 
                                    borderRadius: 1, 
                                    width: '100%',
                                    minWidth: '80px',
                                    bgcolor: 'rgba(220, 220, 220, 0.5)',
                                    '& .MuiLinearProgress-bar': {
                                      bgcolor: servicesProgressPercentage > 66 
                                        ? 'success.main' 
                                        : servicesProgressPercentage > 33 
                                        ? 'warning.main' 
                                        : 'error.main'
                                    }
                                  }} 
                                />
                              </Box>
                            </Box>
                          </Box>
                          
                          {/* Package Description if available */}
                          {pkg.package?.description && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              {pkg.package.description}
                            </Typography>
                          )}
                          
                          <Divider sx={{ my: 1 }} />
                          
                          {/* Services included (now only one service per package) */}
                          {pkg.package?.service_id && (
                            <Box sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                                <StyleIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                                Serviço Incluído:
                              </Typography>
                              <Typography variant="body2" color="text.primary" sx={{ ml: 0.5 }}>
                                {services.find(s => s.id === pkg.package?.service_id)?.name || 'Serviço'}
                              </Typography>
                            </Box>
                          )}

                          {/* Pet associated with the package */}
                          {pkg.pet && (
                            <Box sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                                <PetsIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                                Pet Designado:
                              </Typography>
                              <Typography variant="body2" color="text.primary" sx={{ ml: 0.5 }}>
                                {pkg.pet.name}
                              </Typography>
                            </Box>
                          )}
                          
                          {/* Dates and other info - This Box is now the primary container for dates and buttons below */}
                          {/* Action Buttons will be nested inside this Box for proper ordering */}
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2, alignItems: 'center', justifyContent: 'space-between' }}>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <CalendarTodayIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  Comprado em: {pkg.purchase_date ? formatDate(pkg.purchase_date.toString()) : 'Desconhecido'}
                                </Typography>
                              </Box>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <CalendarTodayIcon sx={{ fontSize: 16, mr: 0.5, color: 'warning.main' }} />
                                  <Typography variant="body2" color={remainingDays !== null && remainingDays < 10 ? 'error.main' : 'warning.main'} sx={{ fontWeight: 'medium' }}>
                                    Expira em: {pkg.expiry_date ? formatDate(pkg.expiry_date.toString()) : 'Sem validade'}
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                            
                            {/* Spacer Box to push buttons to the next line if dates take full width, or to ensure buttons are at bottom if dates are short */}
                            <Box sx={{ width: '100%', mt: 1 }} /> 

                          </Box>{/* End of Dates and other info Box */}

                          {/* Action Buttons - Placed after the main content block for dates and info */}
                          <Box sx={{ display: 'flex', width: '100%', justifyContent: 'space-between', alignItems: 'center', mt: 1, pt:1, borderTop: '1px solid rgba(0,0,0,0.08)' }}>
                            {/* Left-aligned buttons (Resolve, History) */}
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              {pkg.status === 'on_hold_no_show' && (
                                <Tooltip title="Resolver Pendência de Falta">
                                  <Button
                                    size="small"
                                    variant="contained"
                                    color="warning"
                                    startIcon={<AssignmentTurnedInIcon />}
                                    onClick={() => handleOpenResolveDialog(pkg)}
                                  >
                                    Resolver
                                  </Button>
                                </Tooltip>
                              )}
                              <Tooltip title="Ver histórico de uso">
                                <Button
                                  size="small"
                                  variant="outlined"
                                  startIcon={<HistoryIcon />}
                                  onClick={() => handleOpenHistoryDialog(pkg)}
                                >
                                  Histórico
                                </Button>
                              </Tooltip>
                            </Box>

                            {/* Right-aligned button (Delete) */}
                            <Tooltip title="Excluir pacote">
                              <Button
                                size="small"
                                variant="outlined"
                                color="error"
                                startIcon={<DeleteIcon />}
                                onClick={() => handleOpenDeleteDialog(pkg.id)}
                              >
                                Excluir
                              </Button>
                            </Tooltip>
                          </Box>

                        </Paper>
                      </Grid>
                    );
                  })}
                </Grid>
              )}
              {buyError && <Alert severity="warning" sx={{ mt: 2 }}>{buyError}</Alert>}
            </>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={onClose} color="primary">
            Fechar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'error.main' }}>
          <WarningIcon color="error" />
          Confirmar Exclusão do Pacote do Cliente
        </DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body1" fontWeight="medium">
              ATENÇÃO: Esta ação irá REMOVER PERMANENTEMENTE o pacote selecionado para este cliente.
            </Typography>
            <Box component="ul" sx={{ mt: 1, pl: 2 }}>
              <li>O pacote será ocultado da lista e não poderá ser mais visualizado ou interagido.</li>
              <li>Todos os futuros agendamentos VINCULADOS A ESTE PACOTE ESPECÍFICO serão PERMANENTEMENTE EXCLUÍDOS.</li>
              {/* History deletion will be handled based on Big Pappa's decision */}
            </Box>
          </Alert>
          
          <DialogContentText sx={{ mb: 2 }}>
            Esta ação NÃO PODE SER DESFEITA.
            Tem certeza que deseja remover permanentemente este pacote para {customerName}?
          </DialogContentText>
          
          <PetBackflipCaptcha onComplete={handleCaptchaComplete} />
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={handleCloseDeleteDialog}
            disabled={isDeleting}
          >
            Manter Pacote
          </Button>
          <Button 
            onClick={handleConfirmDelete} 
            color="error"
            disabled={!captchaSolved || isDeleting}
            variant="contained"
            startIcon={isDeleting ? <CircularProgress size={20} color="inherit" /> : <DeleteIcon />}
          >
            {isDeleting ? 'Removendo...' : 'Remover Pacote Permanentemente'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Package History Dialog */}
      {selectedPackage && (
        <PackageHistoryDialog
          open={historyDialogOpen}
          onClose={handleCloseHistoryDialog}
          customerPackage={selectedPackage}
          packageName={selectedPackage.package?.name || 'Pacote'}
          customerName={customerName}
          getPackageUsageHistory={getPackageUsageHistory}
        />
      )}

      {packageToResolve && (
        <ResolveNoShowDialog
          open={resolveDialogOpen}
          onClose={handleCloseResolveDialog}
          onResolved={handlePackageResolved}
          customerPackage={packageToResolve}
          customerName={customerName}
        />
      )}

      <BuyPackageDialog
        open={buyDialogOpen}
        onClose={() => setBuyDialogOpen(false)}
        onPurchase={(
          packageId: number,
          petId: number,
          paymentMethod: string,
          firstAppointmentDate: string,
          firstAppointmentTime: string,
          expiryDate?: string
        ): void => {
          void handlePurchase(packageId, petId, paymentMethod, firstAppointmentDate, firstAppointmentTime, expiryDate);
        }}
        packages={packages}
        services={services}
        customerName={customerName}
        loading={packagesLoading || petsLoading}
        error={packagesError || petsError}
        pets={customerPets.map(pet => ({ id: pet.id, name: pet.name }))}
      />
    </>
  );
}; 