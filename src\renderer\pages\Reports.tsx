import React, { useState, useEffect, useMemo } from 'react';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableContainer from '@mui/material/TableContainer';
import Tooltip from '@mui/material/Tooltip';
import { SelectChangeEvent } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import useTheme from '@mui/material/styles/useTheme';
import {
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  Warning as WarningIcon,
  Download as DownloadIcon,
  DateRange as DateRangeIcon,
  ArrowDownward,
  ArrowUpward,
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
} from 'recharts';
import { formatCurrency } from '../types/sales';
import { useSales } from '../hooks/useSales';
import { useProducts } from '../hooks/useProducts';
import { useCustomers } from '../hooks/useCustomers';
import { useServices } from '../hooks/useServices';
import { 
  getBrazilianNow, 
  toBrazilianTimezone,
  formatBrazilianDate
} from '../utils/dateUtils';
import { Product as InventoryProduct } from '../types/inventory';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

// Interface para produto com preço de custo
interface ProductWithCost {
  id: number;
  name: string;
  cost_price?: number;
  [key: string]: any;
}

// Helper type for sales reports
type MonthlySalesData = {
  month: string;
  sales: number;
};

// Helper type for sales by category
type CategoryData = {
  name: string;
  value: number;
};

// Helper type for top products
type TopProductData = {
  name: string;
  sales: number;
  revenue: number;
};

// Helper type for top customers
type TopCustomerData = {
  name: string;
  visits: number;
  totalSpent: number;
};

// Helper type for low stock items
type LowStockData = {
  id: number;
  name: string;
  currentStock: number;
  minStock: number;
};

// Helper type for profitability data
type ProfitabilityData = {
  month: string;
  revenue: number;
  costs: number;
  profit: number;
  margin: number;
};

// Helper type for most profitable products
type ProfitableProductData = {
  name: string;
  revenue: number;
  cost: number;
  profit: number;
  margin: number;
};

// Enhanced sale item type
type EnhancedSaleItem = {
  id: number;
  sale_id: number;
  product_id: number | null;
  service_id: number | null;
  quantity: number;
  price_per_unit: number;
  product?: {
    id: number;
    name: string;
    [key: string]: any;
  } | null;
  service?: {
    id: number;
    name: string;
    [key: string]: any;
  } | null;
  customer_package_id?: number | null;
  is_package_service?: boolean;
};

// Enhanced sale type
type EnhancedSale = {
  id: number;
  customer_id?: number;
  customerId?: number;
  total_amount: number;
  payment_method: string;
  status: string;
  sale_date: string | Date;
  created_at?: string | Date;
  updated_at?: string | Date;
  customer?: any;
  items: EnhancedSaleItem[];
};

// Helper function to enhance sale items
const enhanceSaleWithItems = (
  sale: any, 
  items: any[], 
  products: any[], 
  services: any[]
): EnhancedSale => {
  // Clone the sale to avoid mutating the original object
  const enhancedSale: EnhancedSale = { 
    ...sale,
    items: []
  };
  
  // Add enhanced items
  enhancedSale.items = items.map(item => {
    const enhancedItem: EnhancedSaleItem = { ...item };
    
    // Find and link product
    if (item.product_id) {
      const product = products.find(p => p && p.id === item.product_id);
      if (product) {
        enhancedItem.product = product;
      }
    }
    
    // Find and link service
    if (item.service_id) {
      const service = services.find(s => s && s.id === item.service_id);
      if (service) {
        enhancedItem.service = service;
      }
    }
    
    return enhancedItem;
  });
  
  return enhancedSale;
};

// Define a helper function to render time range text
const getTimeRangeText = (timeRange: string) => {
  return (
    <Typography component="span" color="text.secondary" sx={{ ml: 1, fontSize: '0.8rem' }}>
      ({timeRange === 'week' ? 'Últimos 7 Dias' : 
       timeRange === 'month' ? 'Últimos 30 Dias' : 
       timeRange === 'quarter' ? 'Últimos 3 Meses' : 'Últimos 12 Meses'})
    </Typography>
  );
};

// Componente de Tooltip personalizado para traduzir nomes de campos
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    // Mapeamento de nomes em inglês para português
    const nameTranslations: Record<string, string> = {
      'sales': 'Vendas',
      'value': 'Valor',
      'revenue': 'Receita',
      'costs': 'Custos',
      'profit': 'Lucro',
      'margin': 'Margem'
    };

    return (
      <Paper sx={{ p: 1, boxShadow: 2 }}>
        <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>{label}</Typography>
        {payload.map((item: any, index: number) => {
          // Obter o nome traduzido ou usar o nome original
          const name = nameTranslations[item.name] || item.name;
          return (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Box 
                component="span" 
                sx={{ 
                  width: 10, 
                  height: 10, 
                  backgroundColor: item.color, 
                  display: 'inline-block',
                  mr: 1, 
                  borderRadius: '50%' 
                }} 
              />
              <Typography variant="caption" color="text.secondary">
                {name}: {item.name === 'margin' ? `${Number(item.value).toFixed(2)}%` : formatCurrency(Number(item.value))}
              </Typography>
            </Box>
          );
        })}
      </Paper>
    );
  }
  return null;
};

const Reports: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>('month');
  const [reportType, setReportType] = useState<string>('all');
  const [salesWithItems, setSalesWithItems] = useState<EnhancedSale[]>([]);
  const [isLoadingItems, setIsLoadingItems] = useState<boolean>(false);
  const [packagePurchases, setPackagePurchases] = useState<any[]>([]);
  const [isLoadingPackages, setIsLoadingPackages] = useState<boolean>(false);
  const [activeCustomerPackageIds, setActiveCustomerPackageIds] = useState<number[]>([]); // Added state
  const theme = useTheme();

  // Load real data using hooks
  const { 
    sales, 
    loading: salesLoading, 
    error: salesError,
    getSaleItems 
  } = useSales();
  const { products, loading: productsLoading, error: productsError } = useProducts();
  const { customers, loading: customersLoading, error: customersError } = useCustomers();
  const { services, loading: servicesLoading, error: servicesError } = useServices();

  // Handle loading and error states
  const loading = salesLoading || productsLoading || customersLoading || servicesLoading || isLoadingItems || isLoadingPackages;
  const error = salesError || productsError || customersError || servicesError;

  // Load sales items for all sales
  useEffect(() => {
    const loadAllSaleItems = async () => {
      if (sales && sales.length > 0 && !salesLoading && products.length > 0 && services.length > 0) {
        setIsLoadingItems(true);
        try {
          const salesWithItemsPromises = sales.map(async (sale: any) => {
            // Skip if the sale already has items with proper references
            if (sale.items && Array.isArray(sale.items) && sale.items.length > 0 && 
                sale.items.some((item: any) => item.product || item.service)) {
              return sale as EnhancedSale;
            }
            
            try {
              const items = await getSaleItems(sale.id);
              return enhanceSaleWithItems(sale, items || [], products, services);
            } catch (error) {
              console.error(`Error loading items for sale ${sale.id}:`, error);
              return {
                ...sale,
                items: []
              } as EnhancedSale;
            }
          });
          
          const result = await Promise.all(salesWithItemsPromises);
          
          // Let's log one sale to see its structure
          if (result.length > 0) {
            // console.log('Debug - Enhanced sale example:', {
            //   id: result[0].id,
            //   items: result[0].items ? result[0].items.map((item: EnhancedSaleItem) => ({
            //     id: item.id,
            //     product_id: item.product_id,
            //     service_id: item.service_id,
            //     product: item.product ? { id: item.product.id, name: item.product.name } : null,
            //     service: item.service ? { id: item.service.id, name: item.service.name } : null
            //   })) : []
            // });
          }
          
          setSalesWithItems(result);
        } catch (error) {
          console.error('Error loading sales items:', error);
        } finally {
          setIsLoadingItems(false);
        }
      } else {
        setSalesWithItems([]);
      }
    };
    
    loadAllSaleItems();
  }, [sales, salesLoading, products, services, getSaleItems]);

  // Fetch package purchases and active package IDs based on time range
  useEffect(() => {
    const fetchDataForPackages = async () => {
      setIsLoadingPackages(true);
      try {
        const now = getBrazilianNow();
        const endDate = new Date(now);
        const startDate = new Date(now);

        switch (timeRange) {
          case 'week':
            startDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            startDate.setMonth(now.getMonth() - 1);
            break;
          case 'quarter':
            startDate.setMonth(now.getMonth() - 3);
            break;
          case 'year':
            startDate.setFullYear(now.getFullYear() - 1);
            break;
          default:
            startDate.setMonth(now.getMonth() - 1); // Default to month
        }

        const [packagePurchasesResult, activeIdsResult] = await Promise.all([
          window.electronAPI.invoke(
            'customerPackages:getByPurchaseDateRange',
            startDate.toISOString(),
            endDate.toISOString()
          ),
          window.electronAPI.invoke('customerPackages:getAllActiveIds')
        ]);

        if (packagePurchasesResult.success && Array.isArray(packagePurchasesResult.data)) {
          setPackagePurchases(packagePurchasesResult.data);
        } else {
          console.error('Failed to fetch package purchases:', packagePurchasesResult.error);
          setPackagePurchases([]);
        }

        if (activeIdsResult.success && Array.isArray(activeIdsResult.data)) {
          setActiveCustomerPackageIds(activeIdsResult.data);
        } else {
          console.error('Failed to fetch active package IDs:', activeIdsResult.error);
          setActiveCustomerPackageIds([]);
        }

      } catch (err) {
        console.error('Error fetching package data for reports:', err);
        setPackagePurchases([]);
        setActiveCustomerPackageIds([]);
      }
      setIsLoadingPackages(false);
    };

    fetchDataForPackages();
  }, [timeRange]);

  // Use salesWithItems instead of sales for all data processing
  const salesDataSource = salesWithItems.length > 0 ? salesWithItems : (sales as unknown as EnhancedSale[]);

  // Helper function to parse dates safely
  const parseSaleDate = (saleDate: string | Date): Date => {
    if (!saleDate) return getBrazilianNow(); // Default to current date in Brazil timezone if null/undefined
    
    try {
      // If it's already a Date object
      if (saleDate instanceof Date) return toBrazilianTimezone(saleDate);
      
      // Handle string date
      const date = toBrazilianTimezone(saleDate);
      
      // Validate the date
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date: ${saleDate}, defaulting to current date`);
        return getBrazilianNow();
      }
      
      return date;
    } catch (error) {
      console.error(`Error parsing date: ${saleDate}`, error);
      return getBrazilianNow();
    }
  };

  // Filter sales data based on the selected time range
  const filteredSalesData = useMemo(() => {
    // Create a key that depends on timeRange to ensure we recalculate when it changes
    const filterId = `filter-${timeRange}-${Date.now()}`;
    // console.log(`Creating new filtered data set with ID: ${filterId}`);
    
    if (!salesDataSource || salesDataSource.length === 0) {
      // console.log('No sales data available to filter');
      return [];
    }
    
    const now = getBrazilianNow();
    const cutoffDate = getBrazilianNow();
    
    switch (timeRange) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        cutoffDate.setMonth(now.getMonth() - 1); // Default to month
    }
    
    // console.log(`Time range filter: ${timeRange}`);
    // console.log(`Cutoff date: ${cutoffDate.toISOString()}`);
    // console.log(`Current date: ${now.toISOString()}`);
    // console.log(`Total sales before filtering: ${salesDataSource.length}`);
    
    const filtered = salesDataSource.filter((sale: EnhancedSale) => {
      // Only include paid sales in reports
      if (sale.status && sale.status !== 'paid') {
        return false;
      }
      
      // Use our safe parser
      const saleDate = parseSaleDate(sale.sale_date);
      const isInRange = saleDate >= cutoffDate && saleDate <= now;
      
      // Log a few examples to debug
      if (salesDataSource.indexOf(sale) < 3) {
        // console.log(`Sale #${sale.id} date: ${saleDate.toISOString()}, in range: ${isInRange}, status: ${sale.status}`);
      }
      
      return isInRange;
    });
    
    //console.log(`Total sales after filtering: ${filtered.length}`);
    return filtered;
  }, [salesDataSource, timeRange]);

  // Process sales data based on the selected time range
  const monthlySalesData = useMemo(() => {
    // console.log(`Recalculating sales data with time range: ${timeRange}`);
    if ((!filteredSalesData || filteredSalesData.length === 0) && (!packagePurchases || packagePurchases.length === 0)) {
      // console.log('No filtered sales data or package purchases available for time-based chart');
      return [];
    }
    
    // console.log(`Processing ${filteredSalesData.length} sales records and ${packagePurchases.length} package purchases for time-based display`);
    
    // Create appropriate time period grouping based on selected time range
    let timeMap = new Map<string, number>();
    const now = getBrazilianNow();
    
    if (timeRange === 'week') {
      // console.log('Using last 7 days format for week view');
      
      // Create array of the last 7 days ending with today
      const lastSevenDays: {date: Date, label: string}[] = [];
      const dayLabels = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
      
      for (let i = 6; i >= 0; i--) {
        const date = getBrazilianNow();
        date.setDate(now.getDate() - i);
        
        // Format the date as "Day (MM/DD)" for better readability
        const month = date.getMonth() + 1; // getMonth is 0-indexed
        const day = date.getDate();
        const dayName = dayLabels[date.getDay()];
        const label = `${dayName} (${day}/${month})`;
        
        lastSevenDays.push({date, label});
      }
      
      // Initialize all days with 0
      const dayMap = new Map<string, number>();
      lastSevenDays.forEach(day => dayMap.set(day.label, 0));
      
      // Sum sales by specific day
      filteredSalesData.forEach((sale: EnhancedSale) => {
        const saleDate = parseSaleDate(sale.sale_date);
        
        const matchingDay = lastSevenDays.find(day => 
          saleDate.getDate() === day.date.getDate() && 
          saleDate.getMonth() === day.date.getMonth() &&
          saleDate.getFullYear() === day.date.getFullYear()
        );
        
        if (matchingDay) {
          let adjustedSaleTotal = 0;
          (sale.items || []).forEach((item: EnhancedSaleItem) => {
            if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
              // Skip item from a soft-deleted package
            } else {
              adjustedSaleTotal += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
            }
          });
          const currentTotal = dayMap.get(matchingDay.label) || 0;
          dayMap.set(matchingDay.label, currentTotal + adjustedSaleTotal);
        }
      });

      // Add package purchase revenue for week view (daily breakdown)
      packagePurchases.forEach((pkg: any) => {
        if (pkg.package && typeof pkg.package.price === 'number' && pkg.purchase_date) {
          const purchaseDate = parseSaleDate(pkg.purchase_date);
          const matchingDay = lastSevenDays.find(day => 
            purchaseDate.getDate() === day.date.getDate() && 
            purchaseDate.getMonth() === day.date.getMonth() &&
            purchaseDate.getFullYear() === day.date.getFullYear()
          );
          if (matchingDay) {
            let adjustedSaleTotal = 0;
            (pkg.package.items || []).forEach((item: EnhancedSaleItem) => {
              if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
                // Skip item from a soft-deleted package
              } else {
                adjustedSaleTotal += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
              }
            });
            const currentTotal = dayMap.get(matchingDay.label) || 0;
            dayMap.set(matchingDay.label, currentTotal + adjustedSaleTotal);
          }
        }
      });
      
      // Get the day labels in chronological order
      const sortedLabels = lastSevenDays.map(day => day.label);
      
      // Return data sorted by day order
      timeMap = dayMap;
      
      const result = Array.from(timeMap.entries())
        .map(([period, sales]) => ({ month: period, sales }))
        .sort((a, b) => sortedLabels.indexOf(a.month) - sortedLabels.indexOf(b.month));
      
      // console.log('Week view data:', result);
      return result;
    } 
    else if (timeRange === 'month') {
      // console.log('Using simplified weekly format for month view');
      // For month view (last 30 days), use more intuitive week labels
      const dateMap = new Map<string, number>();
      
      // Create simpler, more intuitive weekly labels
      const weekLabels = ['4 Semanas Atrás', '3 Semanas Atrás', '2 Semanas Atrás', 'Semana Passada', 'Semana Atual'];
      
      // Initialize all periods with 0
      weekLabels.forEach(label => dateMap.set(label, 0));
      
      // Create date ranges for each week
      const weekRanges: {startDate: Date, endDate: Date, label: string}[] = [];
      
      for (let i = 4; i >= 0; i--) {
        const startDate = getBrazilianNow();
        startDate.setDate(now.getDate() - (i * 7 + 6));
        const endDate = getBrazilianNow();
        endDate.setDate(now.getDate() - (i * 7));
        
        weekRanges.push({
          startDate,
          endDate,
          label: weekLabels[4 - i]
        });
      }
      
      // Group sales into weekly chunks
      filteredSalesData.forEach((sale: EnhancedSale) => {
        const saleDate = parseSaleDate(sale.sale_date);
        
        const matchingWeek = weekRanges.find(week => 
          saleDate >= week.startDate && saleDate <= week.endDate
        );
        
        if (matchingWeek) {
          let adjustedSaleTotal = 0;
          (sale.items || []).forEach((item: EnhancedSaleItem) => {
            if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
              // Skip item from a soft-deleted package
            } else {
              adjustedSaleTotal += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
            }
          });
          const currentTotal = dateMap.get(matchingWeek.label) || 0;
          dateMap.set(matchingWeek.label, currentTotal + adjustedSaleTotal);
        }
      });

      // Add package purchase revenue for month view (weekly breakdown)
      packagePurchases.forEach((pkg: any) => {
        if (pkg.package && typeof pkg.package.price === 'number' && pkg.purchase_date) {
          const purchaseDate = parseSaleDate(pkg.purchase_date);
          const matchingWeek = weekRanges.find(week => 
            purchaseDate >= week.startDate && purchaseDate <= week.endDate
          );
          if (matchingWeek) {
            let adjustedSaleTotal = 0;
            (pkg.package.items || []).forEach((item: EnhancedSaleItem) => {
              if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
                // Skip item from a soft-deleted package
              } else {
                adjustedSaleTotal += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
              }
            });
            const currentTotal = dateMap.get(matchingWeek.label) || 0;
            dateMap.set(matchingWeek.label, currentTotal + adjustedSaleTotal);
          }
        }
      });
      
      // Return data sorted by week order
      timeMap = dateMap;
      
      const result = Array.from(timeMap.entries())
        .map(([period, sales]) => ({ month: period, sales }))
        .sort((a, b) => weekLabels.indexOf(a.month) - weekLabels.indexOf(b.month));
      
      // console.log('Month view data:', result);
      return result;
    } 
    else if (timeRange === 'quarter' || timeRange === 'year') {
      // console.log(`Using monthly format for ${timeRange} view`);
      
      // Determine how many months to show
      const monthsToShow = timeRange === 'quarter' ? 3 : 12;
      
      // Create array of relevant months ending with current month
      const relevantMonths: {date: Date, label: string}[] = [];
      const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
      
      for (let i = monthsToShow - 1; i >= 0; i--) {
        const date = getBrazilianNow();
        date.setDate(1); // Set to first day of month
        date.setMonth(now.getMonth() - i);
        
        // Format as "Mon YYYY" to distinguish between same months in different years
        const monthName = monthNames[date.getMonth()];
        const year = date.getFullYear();
        const label = `${monthName} ${year}`;
        
        relevantMonths.push({date, label});
      }
      
      // Initialize all months with 0
      const monthMap = new Map<string, number>();
      relevantMonths.forEach(month => monthMap.set(month.label, 0));
      
      // Sum sales by month
      filteredSalesData.forEach((sale: EnhancedSale) => {
        const saleDate = parseSaleDate(sale.sale_date);
        
        const matchingMonth = relevantMonths.find(month => 
          saleDate.getMonth() === month.date.getMonth() &&
          saleDate.getFullYear() === month.date.getFullYear()
        );
        
        if (matchingMonth) {
          let adjustedSaleTotal = 0;
          (sale.items || []).forEach((item: EnhancedSaleItem) => {
            if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
              // Skip item from a soft-deleted package
            } else {
              adjustedSaleTotal += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
            }
          });
          const currentTotal = monthMap.get(matchingMonth.label) || 0;
          monthMap.set(matchingMonth.label, currentTotal + adjustedSaleTotal);
        }
      });

      // Add package purchase revenue for quarter/year view (monthly breakdown)
      packagePurchases.forEach((pkg: any) => {
        if (pkg.package && typeof pkg.package.price === 'number' && pkg.purchase_date) {
          const purchaseDate = parseSaleDate(pkg.purchase_date);
          const matchingMonth = relevantMonths.find(month => 
            purchaseDate.getMonth() === month.date.getMonth() &&
            purchaseDate.getFullYear() === month.date.getFullYear()
          );
          if (matchingMonth) {
            let adjustedSaleTotal = 0;
            (pkg.package.items || []).forEach((item: EnhancedSaleItem) => {
              if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
                // Skip item from a soft-deleted package
              } else {
                adjustedSaleTotal += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
              }
            });
            const currentTotal = monthMap.get(matchingMonth.label) || 0;
            monthMap.set(matchingMonth.label, currentTotal + adjustedSaleTotal);
          }
        }
      });
      
      // Get the month labels in chronological order
      const sortedLabels = relevantMonths.map(month => month.label);
      
      // Return data sorted by month order
      timeMap = monthMap;
      
      const result = Array.from(timeMap.entries())
        .map(([period, sales]) => ({ month: period, sales }))
        .sort((a, b) => sortedLabels.indexOf(a.month) - sortedLabels.indexOf(b.month));
      
      // console.log(`${timeRange} view data:`, result);
      return result;
    }
    
    // Return an empty array if no valid time range is selected
    // console.log('No valid time range selected, returning empty dataset');
    return [];
  }, [filteredSalesData, timeRange, packagePurchases, activeCustomerPackageIds]); // Added activeCustomerPackageIds dependency

  // Calculate category data directly from sales
  const categoryData = useMemo(() => {
    if ((!filteredSalesData || filteredSalesData.length === 0) && (!packagePurchases || packagePurchases.length === 0)) {
      return [] as CategoryData[];
    }
    // if (!products || products.length === 0) { // Products are not strictly needed if we only have services and packages
    //   return [] as CategoryData[];
    // }

    const categoryMap = new Map<string, number>();

    filteredSalesData.forEach((sale: EnhancedSale) => {
      (sale.items || []).forEach((item: EnhancedSaleItem) => {
        if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
          return; // Skip item from a soft-deleted package
        }
        if (item.product && item.product.category) {
          const value = Number(item.price_per_unit || 0) * Number(item.quantity || 0);
          categoryMap.set(
            item.product.category,
            (categoryMap.get(item.product.category) || 0) + value
          );
        } else if (item.service && item.service.name) {
          // If you want to categorize services, do it here. Example: by service name itself
          // For now, services are not explicitly added to this category chart unless they are part of a general 'Serviços' category if one exists
        }
      });
    });

    // Add revenue from purchased packages
    let totalPackageRevenue = 0;
    packagePurchases.forEach((pkgPurchase: any) => {
      if (pkgPurchase.package && typeof pkgPurchase.package.price === 'number') {
        // Ensure this package purchase falls within the filteredSalesData date range if necessary for consistency
        // or simply add all package purchases within the selected `timeRange`
        totalPackageRevenue += Number(pkgPurchase.package.price);
      }
    });

    if (totalPackageRevenue > 0) {
      categoryMap.set('Pacotes', (categoryMap.get('Pacotes') || 0) + totalPackageRevenue);
    }

    return Array.from(categoryMap.entries()).map(([name, value]) => ({ name, value }));
  }, [filteredSalesData, products, packagePurchases, activeCustomerPackageIds]); // Added activeCustomerPackageIds dependency

  // Calculate top products data
  const topProductsData = useMemo(() => {
    if ((!filteredSalesData || filteredSalesData.length === 0) && (!packagePurchases || packagePurchases.length === 0)) {
      return [] as TopProductData[];
    }

    const productSales = new Map<string, { sales: number; revenue: number; id: number | null }>();

    filteredSalesData.forEach((sale: EnhancedSale) => {
      (sale.items || []).forEach((item: EnhancedSaleItem) => {
        if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
          return; // Skip item from a soft-deleted package
        }
        if (item.product) {
          const current = productSales.get(item.product.name) || { sales: 0, revenue: 0, id: item.product_id };
          current.sales += Number(item.quantity || 0);
          current.revenue += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
          productSales.set(item.product.name, current);
        }
      });
    });

    // Consider if package definitions (as products/services) should be in top products
    // For now, direct package purchases are not itemized as products in this chart.

    return Array.from(productSales.entries())
      .map(([name, data]) => ({ name, ...data }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
  }, [filteredSalesData, packagePurchases, activeCustomerPackageIds]); // Added activeCustomerPackageIds dependency

  const topServicesData = useMemo(() => {
    if ((!filteredSalesData || filteredSalesData.length === 0) && (!packagePurchases || packagePurchases.length === 0)) {
      return [] as TopProductData[]; // Reusing TopProductData structure for simplicity
    }

    const serviceSales = new Map<string, { sales: number; revenue: number; id: number | null }>();

    filteredSalesData.forEach((sale: EnhancedSale) => {
      (sale.items || []).forEach((item: EnhancedSaleItem) => {
        if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
          return; // Skip item from a soft-deleted package
        }
        if (item.service) {
          const current = serviceSales.get(item.service.name) || { sales: 0, revenue: 0, id: item.service_id };
          current.sales += Number(item.quantity || 0); // Typically 1 for services
          current.revenue += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
          serviceSales.set(item.service.name, current);
        }
      });
    });

    // Add revenue from purchased packages (as a general "Pacotes de Serviços" category if desired)
    packagePurchases.forEach((pkgPurchase: any) => {
      if (pkgPurchase.package && pkgPurchase.package.name && pkgPurchase.package.price) {
        const name = `Pacote: ${pkgPurchase.package.name}`;
        const current = serviceSales.get(name) || { sales: 0, revenue: 0, id: null }; // id is null for aggregate package category
        current.sales += 1; // Count each package purchase as one "sale" of this type
        current.revenue += Number(pkgPurchase.package.price);
        serviceSales.set(name, current);
      }
    });

    return Array.from(serviceSales.entries())
      .map(([name, data]) => ({ name, ...data }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
  }, [filteredSalesData, packagePurchases, activeCustomerPackageIds]); // Added activeCustomerPackageIds dependency

  // Calculate top customers data
  const topCustomersData = useMemo(() => {
    if ((!filteredSalesData || filteredSalesData.length === 0) && (!packagePurchases || packagePurchases.length === 0)) {
      return [] as TopCustomerData[];
    }
    if (!customers || customers.length === 0) return [] as TopCustomerData[];

    const customerMetrics = new Map<number, { name: string; visits: number; totalSpent: number }>();

    filteredSalesData.forEach((sale: EnhancedSale) => {
      const customerId = sale.customer?.id || sale.customer_id;
      if (customerId) {
        const customerName = customers.find(c => c.id === customerId)?.name || 'Cliente Desconhecido';
        const current = customerMetrics.get(customerId) || { name: customerName, visits: 0, totalSpent: 0 };
        current.visits += 1;
        
        let adjustedSaleTotal = 0;
        (sale.items || []).forEach((item: EnhancedSaleItem) => {
          if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
            // Skip item from a soft-deleted package
          } else {
            adjustedSaleTotal += Number(item.price_per_unit || 0) * Number(item.quantity || 0);
          }
        });
        current.totalSpent += adjustedSaleTotal;
        customerMetrics.set(customerId, current);
      }
    });

    // Add spending from package purchases to relevant customers
    packagePurchases.forEach((pkgPurchase: any) => {
      if (pkgPurchase.customer_id && pkgPurchase.package && pkgPurchase.package.price) {
        const customerId = pkgPurchase.customer_id;
        const customerName = customers.find(c => c.id === customerId)?.name || 'Cliente Desconhecido';
        const current = customerMetrics.get(customerId) || { name: customerName, visits: 0, totalSpent: 0 };
        // We don't increment visits here as it's a package purchase, not a distinct sale event for this chart.
        current.totalSpent += Number(pkgPurchase.package.price);
        customerMetrics.set(customerId, current);
      }
    });

    return Array.from(customerMetrics.values())
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 10);
  }, [filteredSalesData, customers, packagePurchases, activeCustomerPackageIds]); // Added activeCustomerPackageIds dependency

  // Calculate low stock items
  const lowStockItems = useMemo(() => {
    if (!products) return [];
    
    return products
      .filter((product: any) => product.stock_quantity < product.min_stock_level)
      .map((product: any) => ({
        id: product.id,
        name: product.name,
        currentStock: product.stock_quantity,
        minStock: product.min_stock_level
      }));
  }, [products]);

  // Use a dedicated debugging function to log the exact structure of sales data
  useEffect(() => {
    if (salesDataSource && salesDataSource.length > 0) {
      // console.log('DEBUG - Sales data structure:');
      // Log the first 3 sales or fewer if there are less
      const sampleSize = Math.min(3, salesDataSource.length);
      for (let i = 0; i < sampleSize; i++) {
        const sale = salesDataSource[i];
        // console.log(`Sale #${sale.id} details:`, {
        //   id: sale.id,
        //   date: sale.sale_date,
        //   dateType: typeof sale.sale_date,
        //   total: sale.total_amount,
        //   items: sale.items ? sale.items.length : 0
        // });
      }
    }
  }, [salesDataSource]);

  // Add a specific effect for time range changes
  useEffect(() => {
    // console.log(`Time range changed to: ${timeRange}`);
    // This forces a recalculation of all dependent data when timeRange changes
  }, [timeRange]);

  const handleTimeRangeChange = (event: SelectChangeEvent<string>) => {
    const newTimeRange = event.target.value;
    // console.log(`Changing time range to: ${newTimeRange}`);
    setTimeRange(newTimeRange);
    
    // Force a refresh by temporarily setting loading state
    setIsLoadingItems(true);
    setTimeout(() => {
      setIsLoadingItems(false);
    }, 100);
  };

  const handleReportTypeChange = (event: SelectChangeEvent<string>) => {
    setReportType(event.target.value);
  };

  // Calculate profitability data
  const profitabilityData = useMemo(() => {
    if ((!filteredSalesData || filteredSalesData.length === 0) && (!packagePurchases || packagePurchases.length === 0)) {
      return [] as ProfitabilityData[];
    }
    if (!products || products.length === 0) return [] as ProfitabilityData[];

    const monthlyDataMap = new Map<string, { revenue: number; costs: number }>();
    const now = getBrazilianNow();

    filteredSalesData.forEach((sale: EnhancedSale) => {
      const saleDate = parseSaleDate(sale.sale_date);
      const monthYear = `${saleDate.toLocaleString('default', { month: 'short' })} ${saleDate.getFullYear()}`;

      let currentMonthData = monthlyDataMap.get(monthYear) || { revenue: 0, costs: 0 };

      (sale.items || []).forEach((item: EnhancedSaleItem) => {
        if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
          return; // Skip item from a soft-deleted package
        }

        const itemRevenue = Number(item.price_per_unit || 0) * Number(item.quantity || 0);
        currentMonthData.revenue += itemRevenue;

        if (item.product_id) {
          const productDetails = products.find(p => p.id === item.product_id) as ProductWithCost | undefined;
          if (productDetails && typeof productDetails.cost_price === 'number') {
            currentMonthData.costs += productDetails.cost_price * Number(item.quantity || 0);
          }
        }
        // Service costs are not explicitly tracked per service in this model for now
      });
      monthlyDataMap.set(monthYear, currentMonthData);
    });

    // Add revenue from package purchases (costs for packages are not directly calculated here)
    packagePurchases.forEach((pkgPurchase: any) => {
      if (pkgPurchase.package && pkgPurchase.package.price && pkgPurchase.purchase_date) {
        const purchaseDate = parseSaleDate(pkgPurchase.purchase_date);
        // Ensure purchaseDate is within the report's overall timeRange to avoid double counting if filteredSalesData is more restrictive
        const cutoffDate = getBrazilianNow();
        if (timeRange === 'week') cutoffDate.setDate(now.getDate() - 7);
        else if (timeRange === 'month') cutoffDate.setMonth(now.getMonth() - 1);
        else if (timeRange === 'quarter') cutoffDate.setMonth(now.getMonth() - 3);
        else if (timeRange === 'year') cutoffDate.setFullYear(now.getFullYear() - 1);

        if (purchaseDate >= cutoffDate) {
          const monthYear = `${purchaseDate.toLocaleString('default', { month: 'short' })} ${purchaseDate.getFullYear()}`;
          let currentMonthData = monthlyDataMap.get(monthYear) || { revenue: 0, costs: 0 };
          currentMonthData.revenue += Number(pkgPurchase.package.price);
          // Assuming package cost is not itemized here for simplicity
          monthlyDataMap.set(monthYear, currentMonthData);
        }
      }
    });

    return Array.from(monthlyDataMap.entries())
      .map(([month, data]) => {
        const profit = data.revenue - data.costs;
        const margin = data.revenue > 0 ? (profit / data.revenue) * 100 : 0;
        return { month, ...data, profit, margin };
      })
      .sort((a, b) => new Date(b.month).getTime() - new Date(a.month).getTime()); // Sort by month, recent first
  }, [filteredSalesData, products, packagePurchases, timeRange, activeCustomerPackageIds]); // Added activeCustomerPackageIds dependency

  const overallProfitMetrics = useMemo(() => {
    if (!profitabilityData || profitabilityData.length === 0) {
      return {
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        profitMargin: 0,
        revenueChange: 0, // Placeholder: Requires previous period data for accurate calculation
        profitChange: 0,  // Placeholder: Requires previous period data for accurate calculation
      };
    }

    const totals = profitabilityData.reduce(
      (acc, monthData) => {
        acc.revenue += monthData.revenue;
        acc.costs += monthData.costs;
        acc.profit += monthData.profit;
        return acc;
      },
      { revenue: 0, costs: 0, profit: 0 }
    );

    const profitMargin = totals.revenue > 0 ? (totals.profit / totals.revenue) * 100 : 0;

    // Placeholder for change calculations. A more robust solution would compare to a previous period.
    const revenueChange = 0; 
    const profitChange = 0;

    return {
      totalRevenue: totals.revenue,
      totalCost: totals.costs,
      totalProfit: totals.profit,
      profitMargin,
      revenueChange,
      profitChange,
    };
  }, [profitabilityData, timeRange]); // timeRange might be needed if change calc is implemented

  // Calculate most profitable products
  const mostProfitableProductsData = useMemo(() => {
    if ((!filteredSalesData || filteredSalesData.length === 0)) {
      return [] as ProfitableProductData[];
    }
    if (!products || products.length === 0) return [] as ProfitableProductData[];

    const productProfitMap = new Map<string, { revenue: number; cost: number; profit: number; margin: number }>();

    filteredSalesData.forEach((sale: EnhancedSale) => {
      (sale.items || []).forEach((item: EnhancedSaleItem) => {
        if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
          return; // Skip item from a soft-deleted package
        }
        if (item.product_id) {
          const productDetails = products.find(p => p.id === item.product_id) as ProductWithCost | undefined;
          if (productDetails) {
            const productName = productDetails.name;
            let currentData = productProfitMap.get(productName) || { revenue: 0, cost: 0, profit: 0, margin: 0 };
            
            const itemRevenue = Number(item.price_per_unit || 0) * Number(item.quantity || 0);
            const itemCost = (typeof productDetails.cost_price === 'number' ? productDetails.cost_price : 0) * Number(item.quantity || 0);
            
            currentData.revenue += itemRevenue;
            currentData.cost += itemCost;
            currentData.profit = currentData.revenue - currentData.cost;
            currentData.margin = currentData.revenue > 0 ? (currentData.profit / currentData.revenue) * 100 : 0;
            
            productProfitMap.set(productName, currentData);
          }
        }
      });
    });

    // Package purchases are not itemized by product, so not included here.

    return Array.from(productProfitMap.entries())
      .map(([name, data]) => ({ name, ...data }))
      .sort((a, b) => b.profit - a.profit)
      .slice(0, 10);
  }, [filteredSalesData, products, activeCustomerPackageIds]); // Added activeCustomerPackageIds dependency

  const averageTicketData = useMemo(() => {
    if ((!filteredSalesData || filteredSalesData.length === 0) && (!packagePurchases || packagePurchases.length === 0)) {
      return 0;
    }

    let totalRevenue = 0;
    let totalSales = 0;

    filteredSalesData.forEach((sale: EnhancedSale) => {
      totalRevenue += sale.total_amount;
      totalSales += sale.items ? sale.items.length : 0;
    });

    return totalRevenue / totalSales;
  }, [filteredSalesData, packagePurchases]);

  // Show loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Erro ao carregar dados: {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TrendingUpIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 'bold', 
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: 60,
                height: 4,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 2,
              }
            }}
          >
            Relatórios e Análises
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Período</InputLabel>
            <Select
              value={timeRange}
              label="Período"
              onChange={handleTimeRangeChange}
            >
              <MenuItem value="week">Última Semana</MenuItem>
              <MenuItem value="month">Último Mês</MenuItem>
              <MenuItem value="quarter">Último Trimestre</MenuItem>
              <MenuItem value="year">Último Ano</MenuItem>
            </Select>
          </FormControl>
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Tipo de Relatório</InputLabel>
            <Select
              value={reportType}
              label="Tipo de Relatório"
              onChange={handleReportTypeChange}
            >
              <MenuItem value="all">Todos</MenuItem>
              <MenuItem value="sales">Apenas Vendas</MenuItem>
              <MenuItem value="inventory">Apenas Estoque</MenuItem>
              <MenuItem value="customers">Apenas Clientes</MenuItem>
              <MenuItem value="profitability">Apenas Lucratividade</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Profitability Analysis */}
        {(reportType === 'all' || reportType === 'profitability') && (
          <>
            <Grid item xs={12}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                Análise de Lucratividade de Produtos
              </Typography>
            </Grid>
            
            {/* Profitability Overview Cards */}
            <Grid item xs={12} md={3}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Receita Total
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {formatCurrency(overallProfitMetrics.totalRevenue)}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  {overallProfitMetrics.revenueChange > 0 ? (
                    <ArrowUpward fontSize="small" color="success" />
                  ) : (
                    <ArrowDownward fontSize="small" color="error" />
                  )}
                  <Typography 
                    variant="body2" 
                    color={overallProfitMetrics.revenueChange >= 0 ? 'success.main' : 'error.main'}
                  >
                    {Math.abs(overallProfitMetrics.revenueChange).toFixed(1)}% 
                    {overallProfitMetrics.revenueChange >= 0 ? ' acima' : ' abaixo'} do período anterior
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} md={3}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Lucro Total
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {formatCurrency(overallProfitMetrics.totalProfit)}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  {overallProfitMetrics.profitChange > 0 ? (
                    <ArrowUpward fontSize="small" color="success" />
                  ) : (
                    <ArrowDownward fontSize="small" color="error" />
                  )}
                  <Typography 
                    variant="body2" 
                    color={overallProfitMetrics.profitChange >= 0 ? 'success.main' : 'error.main'}
                  >
                    {Math.abs(overallProfitMetrics.profitChange).toFixed(1)}% 
                    {overallProfitMetrics.profitChange >= 0 ? ' acima' : ' abaixo'} do período anterior
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} md={3}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Custos Totais
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {formatCurrency(overallProfitMetrics.totalCost)}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  <Typography variant="body2" color="text.secondary">
                    Baseado no preço de custo dos produtos
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} md={3}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Margem de Lucro
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {overallProfitMetrics.profitMargin.toFixed(1)}%
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  <Typography variant="body2" color="text.secondary">
                    (Lucro ÷ Receita) × 100
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            
            {/* Profitability Chart */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Receita vs. Custos vs. Lucro
                  {getTimeRangeText(timeRange)}
                </Typography>
                {profitabilityData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart
                      data={profitabilityData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <RechartsTooltip content={<CustomTooltip />} />
                      <Legend />
                      <Bar dataKey="revenue" name="Receita" fill="#8884d8" />
                      <Bar dataKey="costs" name="Custos" fill="#ff8042" />
                      <Bar dataKey="profit" name="Lucro" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <Typography variant="body1" align="center" sx={{ mt: 5 }}>
                    Nenhum dado de lucratividade disponível.
                  </Typography>
                )}
              </Paper>
            </Grid>
            
            {/* Most Profitable Products */}
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Produtos Mais Lucrativos
                </Typography>
                <Box sx={{ flex: 1, overflow: 'auto' }}>
                  {mostProfitableProductsData.length > 0 ? (
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Nome</TableCell>
                            <TableCell align="right">Receita</TableCell>
                            <TableCell align="right">Lucro</TableCell>
                            <TableCell align="right">Margem</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {mostProfitableProductsData.map((product) => (
                            <TableRow key={product.name}>
                              <TableCell>{product.name}</TableCell>
                              <TableCell align="right">{formatCurrency(product.revenue)}</TableCell>
                              <TableCell align="right">{formatCurrency(product.profit)}</TableCell>
                              <TableCell align="right">{product.margin.toFixed(1)}%</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 3, textAlign: 'center' }}>
                      Nenhum dado de lucratividade disponível. Complete algumas vendas com produtos para calcular a lucratividade.
                    </Typography>
                  )}
                </Box>
              </Paper>
            </Grid>
            
            {/* Profit Margin Trend */}
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Tendência de Margem de Lucro
                  {getTimeRangeText(timeRange)}
                </Typography>
                <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  {profitabilityData.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={profitabilityData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis domain={[0, 'dataMax + 10']} unit="%" />
                        <RechartsTooltip content={<CustomTooltip />} />
                        <Line 
                          type="monotone" 
                          dataKey="margin" 
                          name="Margem" 
                          stroke="#ff7300" 
                          activeDot={{ r: 8 }} 
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 3, textAlign: 'center' }}>
                      Nenhum dado de margem de lucro disponível. Complete vendas com produtos para ver a tendência da margem de lucro.
                    </Typography>
                  )}
                </Box>
              </Paper>
            </Grid>
          </>
        )}

        {/* Sales Overview */}
        {(reportType === 'all' || reportType === 'sales') && (
          <>
            <Grid item xs={12}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                Visão Geral de Vendas
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Visão Geral de Vendas
                </Typography>
                {monthlySalesData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={monthlySalesData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <RechartsTooltip content={<CustomTooltip />} />
                      <Line type="monotone" dataKey="sales" stroke="#8884d8" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <Typography variant="body1" align="center" sx={{ mt: 5 }}>
                    Nenhum dado de venda disponível.
                  </Typography>
                )}
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Produtos/Serviços Mais Vendidos
                </Typography>
                <Box sx={{ flex: 1, overflow: 'auto' }}>
                  {topProductsData.length > 0 ? (
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Nome</TableCell>
                            <TableCell align="right">Unidades Vendidas</TableCell>
                            <TableCell align="right">Receita</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {topProductsData.map((product) => (
                            <TableRow key={product.name}>
                              <TableCell>{product.name}</TableCell>
                              <TableCell align="right">{product.sales}</TableCell>
                              <TableCell align="right">R$ {product.revenue.toFixed(2)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 3, textAlign: 'center' }}>
                      Nenhum dado de venda disponível. Complete algumas vendas para ver seus produtos mais vendidos.
                    </Typography>
                  )}
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom>
                  Vendas por Categoria
                  {getTimeRangeText(timeRange)}
                  {categoryData.length === 0 && <span style={{ color: 'grey', fontSize: '0.9rem' }}>(Nenhum dado encontrado)</span>}
                </Typography>
                <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  {categoryData.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={categoryData}
                          dataKey="value"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={100}
                          fill="#8884d8"
                          label={false}
                        >
                          {categoryData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <RechartsTooltip content={<CustomTooltip />} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 3, textAlign: 'center' }}>
                      Nenhum dado de categoria disponível. Complete vendas com produtos categorizados para ver este gráfico.
                    </Typography>
                  )}
                </Box>
              </Paper>
            </Grid>
          </>
        )}

        {/* Customer Analytics */}
        {(reportType === 'all' || reportType === 'customers') && (
          <>
            <Grid item xs={12}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                Análise de Clientes
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Principais Clientes
                  {getTimeRangeText(timeRange)}
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Nome do Cliente</TableCell>
                        <TableCell align="right">Quantidade de Visitas</TableCell>
                        <TableCell align="right">Total Gasto</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {topCustomersData.length > 0 ? (
                        topCustomersData.map((customer) => (
                          <TableRow key={customer.name}>
                            <TableCell>{customer.name}</TableCell>
                            <TableCell align="right">{customer.visits}</TableCell>
                            <TableCell align="right">{formatCurrency(customer.totalSpent)}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={3} align="center">
                            Nenhum dado de cliente disponível.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>
          </>
        )}

        {/* Inventory Status */}
        {(reportType === 'all' || reportType === 'inventory') && (
          <>
            <Grid item xs={12}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                Status do Estoque
              </Typography>
            </Grid>
            <Grid item xs={12} md={12}>
              <Paper sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>Alertas de Estoque Baixo</Typography>
                <TableContainer sx={{ flex: 1 }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Produto</TableCell>
                        <TableCell align="right">Estoque Atual</TableCell>
                        <TableCell align="right">Estoque Mínimo</TableCell>
                        <TableCell align="right">Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {lowStockItems.length > 0 ? (
                        lowStockItems.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell>{item.name}</TableCell>
                            <TableCell align="right">{item.currentStock}</TableCell>
                            <TableCell align="right">{item.minStock}</TableCell>
                            <TableCell align="right">
                              <Tooltip title="Estoque Baixo">
                                <WarningIcon color="warning" />
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} align="center">
                            Não há itens com estoque baixo.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
};

export default Reports; 