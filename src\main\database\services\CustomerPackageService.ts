import { AppDataSource } from '../connection';
import { CustomerPackage, CustomerPackageStatus } from '../models/CustomerPackage';
import { Package } from '../models/Package';
import { Appointment } from '../models/Appointment';
import { PackageUsageHistory, PackageUsageStatus } from '../models/PackageUsageHistory';
import { Repository } from 'typeorm';
import { PetService } from '../models/PetService';
import { Service } from '../models/Service';
import { Between, IsNull } from 'typeorm';

export class CustomerPackageService {
  private repository: Repository<CustomerPackage>;
  private packageRepository: Repository<Package>;
  private appointmentRepository: Repository<Appointment>;
  private usageHistoryRepository: Repository<PackageUsageHistory>;
  private petServiceRepository: Repository<PetService>;
  private serviceRepository: Repository<Service>;

  constructor() {
    this.repository = AppDataSource.getRepository(CustomerPackage);
    this.packageRepository = AppDataSource.getRepository(Package);
    this.appointmentRepository = AppDataSource.getRepository(Appointment);
    this.usageHistoryRepository = AppDataSource.getRepository(PackageUsageHistory);
    this.petServiceRepository = AppDataSource.getRepository(PetService);
    this.serviceRepository = AppDataSource.getRepository(Service);
  }

  async assignPackageToCustomer(
    customerId: number,
    packageId: number,
    petId: number,
    purchaseDate: Date,
    notes?: string,
    expiryDate?: Date | null,
    pricePaid?: number
  ): Promise<CustomerPackage> {
    const pkg = await this.packageRepository.findOneByOrFail({ id: packageId });
    const customerPackage = this.repository.create({
      customer_id: customerId,
      package_id: packageId,
      pet_id: petId,
      status: 'pending_activation',
      remaining_occurrences: pkg.total_occurrences,
      purchase_date: purchaseDate,
      expiry_date: expiryDate || null,
      price_paid: pricePaid !== undefined ? pricePaid : pkg.price,
      notes: notes || null,
      activation_date: null,
      next_scheduled_appointment_id: null,
      current_cycle_first_scheduled_date: null
    });
    return this.repository.save(customerPackage);
  }

  async updateCustomerPackagePrice(customerPackageId: number, newPrice: number): Promise<CustomerPackage | null> {
    const customerPackage = await this.repository.findOneBy({ id: customerPackageId });
    if (!customerPackage) return null;

    customerPackage.price_paid = newPrice;
    customerPackage.updated_at = new Date();

    return this.repository.save(customerPackage);
  }

  async migratePricePaidField(): Promise<void> {
    // Find all customer packages that don't have a price_paid value
    const packagesWithoutPrice = await this.repository.find({
      where: { price_paid: IsNull() },
      relations: ['package']
    });

    for (const customerPackage of packagesWithoutPrice) {
      if (customerPackage.package) {
        customerPackage.price_paid = customerPackage.package.price;
        await this.repository.save(customerPackage);
      }
    }
  }

  async activateCustomerPackage(customerPackageId: number, firstAppointmentDate: Date): Promise<CustomerPackage | null> {
    const customerPackage = await this.repository.findOneBy({ id: customerPackageId });
    if (!customerPackage || customerPackage.status !== 'pending_activation') return null;
    customerPackage.status = 'active';
    customerPackage.activation_date = firstAppointmentDate;
    customerPackage.current_cycle_first_scheduled_date = firstAppointmentDate;
    // Schedule first appointment
    const pkg = await this.packageRepository.findOneByOrFail({ id: customerPackage.package_id });
    const appointment = this.appointmentRepository.create({
      customer: { id: customerPackage.customer_id } as any,
      pet: { id: customerPackage.pet_id } as any,
      service: { id: pkg.service_id } as any,
      appointment_date: firstAppointmentDate,
      status: 'scheduled',
      is_package_appointment: true,
      source_customer_package_id: customerPackage.id
    });
    const savedAppointment = await this.appointmentRepository.save(appointment);
    customerPackage.next_scheduled_appointment_id = savedAppointment.id;
    await this.repository.save(customerPackage);
    return customerPackage;
  }

  async getCustomerPackages(customerId: number): Promise<CustomerPackage[]> {
    return this.repository.find({
      where: { customer_id: customerId, is_deleted: false },
      relations: ['package', 'pet', 'nextScheduledAppointment'],
      order: { created_at: 'DESC' }
    });
  }

  async getUpcomingPackageAppointments(): Promise<Appointment[]> {
    return this.appointmentRepository.find({
      where: { is_package_appointment: true, status: 'scheduled' },
      relations: ['customer', 'pet', 'service'],
      order: { appointment_date: 'ASC' }
    });
  }

  async getPackagesOnHold(): Promise<CustomerPackage[]> {
    return this.repository.find({
      where: [
        { status: 'on_hold_no_show', is_deleted: false },
        { status: 'on_hold_customer_request', is_deleted: false }
      ],
      relations: ['package', 'pet', 'nextScheduledAppointment'],
      order: { updated_at: 'DESC' }
    });
  }

  async getPackagesByPurchaseDateRange(startDate: Date, endDate: Date): Promise<CustomerPackage[]> {
    return this.repository.find({
      where: {
        purchase_date: Between(startDate, endDate),
        is_deleted: false,
        // We might only want to count packages that were actually activated or paid,
        // but for now, let's include all packages purchased in the range.
        // Consider adding a status filter if needed, e.g., status: Not('pending_activation')
      },
      relations: ['package'], // Ensure 'package' relation is loaded to access the price
      order: { purchase_date: 'ASC' }
    });
  }

  /**
   * Called when a package appointment is completed. Handles recurrence.
   * @param appointmentId The completed appointment's ID
   */
  async handlePackageAppointmentCompletion(appointmentId: number): Promise<void> {
    // Fetch the completed appointment
    const appointment = await this.appointmentRepository.findOneBy({ id: appointmentId });
    if (!appointment || !appointment.is_package_appointment || !appointment.source_customer_package_id) return;
    // Only proceed if status is 'completed'
    if (appointment.status !== 'completed') return;

    // Fetch the customer package
    const customerPackage = await this.repository.findOneBy({ id: appointment.source_customer_package_id });
    if (!customerPackage || customerPackage.status !== 'active') return;

    // Fetch the package definition
    const pkg = await this.packageRepository.findOneByOrFail({ id: customerPackage.package_id });

    // --- Add PetService log START ---
    try {
      const originalService = await this.serviceRepository.findOneBy({ id: pkg.service_id });
      if (originalService) {
        const petServiceLog = this.petServiceRepository.create({
          pet_id: customerPackage.pet_id,
          service_name: originalService.name,
          service_date: new Date(appointment.appointment_date), // Ensure it's a Date object
          notes: `Serviço concluído como parte do pacote: ${pkg.name}`
        });
        await this.petServiceRepository.save(petServiceLog);
      } else {
        console.warn(`CustomerPackageService: Could not find original service with ID ${pkg.service_id} to log PetService for package appointment ${appointmentId}`);
      }
    } catch (error) {
      console.error(`CustomerPackageService: Error logging PetService for package appointment ${appointmentId}:`, error);
    }
    // --- Add PetService log END ---

    // Decrement remaining_occurrences
    customerPackage.remaining_occurrences = Math.max(0, customerPackage.remaining_occurrences - 1);
    // If no occurrences left, mark as completed
    if (customerPackage.remaining_occurrences <= 0) {
      customerPackage.status = 'completed';
      customerPackage.next_scheduled_appointment_id = null;
      await this.repository.save(customerPackage);
      return;
    }
    // Calculate next appointment date
    const lastDate = new Date(appointment.appointment_date);
    let nextDate = new Date(lastDate);
    // Map frequency_type to recurrence logic
    // 'weekly' => every X weeks, 'monthly' => every X months, 'custom_days' => every X days (daily)
    switch (pkg.frequency_type) {
      case 'custom_days':
        nextDate.setDate(nextDate.getDate() + pkg.frequency_interval);
        break;
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7 * pkg.frequency_interval);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + pkg.frequency_interval);
        break;
      default:
        // Unsupported frequency_type
        return;
    }
    // Check expiry (if expiry_date is set)
    if (customerPackage.expiry_date && nextDate > customerPackage.expiry_date) {
      customerPackage.status = 'completed';
      customerPackage.next_scheduled_appointment_id = null;
      await this.repository.save(customerPackage);
      return;
    }
    // Schedule next appointment
    const nextAppointment = this.appointmentRepository.create({
      customer: { id: customerPackage.customer_id } as any,
      pet: { id: customerPackage.pet_id } as any,
      service: { id: pkg.service_id } as any,
      appointment_date: nextDate,
      status: 'scheduled',
      is_package_appointment: true,
      source_customer_package_id: customerPackage.id
    });
    const savedNext = await this.appointmentRepository.save(nextAppointment);
    customerPackage.next_scheduled_appointment_id = savedNext.id;
    await this.repository.save(customerPackage);
  }

  /**
   * Handler para marcar no-show de agendamento de pacote.
   * Não decrementa occurrences, apenas pausa o pacote e loga o evento.
   */
  async handlePackageAppointmentNoShow(appointmentId: number): Promise<void> {
    // Busca o agendamento
    const appointment = await this.appointmentRepository.findOneBy({ id: appointmentId });
    if (!appointment || !appointment.is_package_appointment || !appointment.source_customer_package_id) return;
    // Só processa se status for 'no_show'
    if (appointment.status !== 'no_show') return;
    // Busca o pacote do cliente
    const customerPackage = await this.repository.findOneBy({ id: appointment.source_customer_package_id });
    if (!customerPackage) return;
    // Pausa o pacote
    customerPackage.status = 'on_hold_no_show';
    await this.repository.save(customerPackage);
    // Loga no histórico de uso
    const usage = this.usageHistoryRepository.create({
      customer_package_id: customerPackage.id,
      appointment_id: appointment.id,
      service_date: appointment.appointment_date,
      status_at_usage: 'no_show',
      notes: 'No-show automático ou manual',
    });
    await this.usageHistoryRepository.save(usage);
    // Não decrementa occurrences nem agenda próximo serviço
  }

  async resolveNoShowWithReschedule(
    customerPackageId: number,
    newAppointmentDateTime: Date,
    adminNotes?: string
  ): Promise<CustomerPackage | null> {
    const customerPackage = await this.repository.findOne({
      where: { id: customerPackageId },
      relations: ['package'], // Ensure package relation is loaded for service_id
    });

    if (!customerPackage) {
      console.error(`resolveNoShowWithReschedule: CustomerPackage with ID ${customerPackageId} not found.`);
      return null;
    }
    if (customerPackage.status !== 'on_hold_no_show') {
      console.warn(`resolveNoShowWithReschedule: CustomerPackage ${customerPackageId} is not on_hold_no_show, current status: ${customerPackage.status}.`);
      return null; // Or throw error, depending on desired strictness
    }
    if (!customerPackage.package) {
        console.error(`resolveNoShowWithReschedule: Base Package details not found for CustomerPackage ID ${customerPackageId}.`);
        return null;
    }

    customerPackage.status = 'active';

    // Schedule new appointment
    const newAppointment = this.appointmentRepository.create({
      customer: { id: customerPackage.customer_id } as any,
      pet: { id: customerPackage.pet_id } as any,
      service: { id: customerPackage.package.service_id } as any,
      appointment_date: newAppointmentDateTime,
      status: 'scheduled',
      is_package_appointment: true,
      source_customer_package_id: customerPackage.id,
    });
    const savedNewAppointment = await this.appointmentRepository.save(newAppointment);
    customerPackage.next_scheduled_appointment_id = savedNewAppointment.id;

    await this.repository.save(customerPackage);

    // Log usage history
    const usageHistory = this.usageHistoryRepository.create({
      customer_package_id: customerPackage.id,
      appointment_id: savedNewAppointment.id, // Link to the new appointment
      service_date: newAppointmentDateTime,
      event_type: 'no_show_resolved_rescheduled',
      status_at_usage: 'active',
      notes: `Falta Resolvida. Próximo agendamento para ${newAppointmentDateTime.toLocaleString('pt-BR')}.${adminNotes ? `\nObservações: ${adminNotes}` : ''}`,
    });
    await this.usageHistoryRepository.save(usageHistory);

    return customerPackage;
  }

  async resolveNoShowByCancelling(
    customerPackageId: number,
    adminNotes?: string
  ): Promise<CustomerPackage | null> {
    const customerPackage = await this.repository.findOneBy({ id: customerPackageId });

    if (!customerPackage) {
      console.error(`resolveNoShowByCancelling: CustomerPackage with ID ${customerPackageId} not found.`);
      return null;
    }
    if (customerPackage.status !== 'on_hold_no_show') {
      console.warn(`resolveNoShowByCancelling: CustomerPackage ${customerPackageId} is not on_hold_no_show, current status: ${customerPackage.status}.`);
      return null;
    }

    customerPackage.status = 'cancelled';
    customerPackage.next_scheduled_appointment_id = null;
    await this.repository.save(customerPackage);

    // Log usage history
    const usageHistory = this.usageHistoryRepository.create({
      customer_package_id: customerPackage.id,
      // appointment_id might be tricky here. The original no-show appointment? or null? Let's find the original no-show appointment if possible.
      // For now, let's find an appointment that caused this no_show state.
      // This requires a bit more logic - potentially querying appointments linked to this package with no_show status.
      // For simplicity in this step, we'll set a placeholder, this should be reviewed.
      // Ideally, the original appointment_id that triggered the no-show would be passed or easily found.
      appointment_id: customerPackage.next_scheduled_appointment_id || 0, // Placeholder - needs refinement
      service_date: new Date(), // Date of cancellation
      event_type: 'no_show_resolved_cancelled',
      status_at_usage: 'cancelled',
      notes: `Falta Resolvida. Pacote cancelado.${adminNotes ? `\nObservações: ${adminNotes}` : ''}`,
    });
    await this.usageHistoryRepository.save(usageHistory);

    return customerPackage;
  }

  async cancelCustomerPackageByUser(customerPackageId: number, adminNotes?: string): Promise<CustomerPackage | null> {
    const customerPackage = await this.repository.findOneBy({ id: customerPackageId });

    if (!customerPackage) {
      console.error(`cancelCustomerPackageByUser: CustomerPackage with ID ${customerPackageId} not found.`);
      return null;
    }

    // Allow cancellation for active, pending_activation, on_hold, or already cancelled packages
    const cancellableStatuses: CustomerPackageStatus[] = ['active', 'pending_activation', 'on_hold_no_show', 'on_hold_customer_request', 'cancelled'];
    if (!cancellableStatuses.includes(customerPackage.status as CustomerPackageStatus)) {
      console.warn(`cancelCustomerPackageByUser: CustomerPackage ${customerPackageId} is not in a cancellable state. Current status: ${customerPackage.status}.`);
      // Optionally, throw an error or return a specific response indicating non-cancellable state
      // For now, returning null or the unchanged package
      return customerPackage; // Or return null if strict cancellation state is required
    }

    const originalStatus = customerPackage.status;
    customerPackage.status = 'cancelled'; // Using the existing 'cancelled' status
    customerPackage.is_deleted = true; // Set is_deleted to true
    customerPackage.remaining_occurrences = 0;
    customerPackage.next_scheduled_appointment_id = null; // Clear next scheduled appointment
    customerPackage.updated_at = new Date();

    // Delete related package usage history first
    await this.usageHistoryRepository.delete({ customer_package_id: customerPackageId });

    // Hard-delete future scheduled appointments linked to this package
    const futureAppointments = await this.appointmentRepository.find({
      where: {
        source_customer_package_id: customerPackageId,
        status: 'scheduled',
      }
    });

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today to start of day for comparison

    for (const appt of futureAppointments) {
      const apptDate = new Date(appt.appointment_date);
      if (apptDate >= today) { // Only delete appointments from today onwards
        await this.appointmentRepository.delete(appt.id); // Hard delete
      }
    }

    await this.repository.save(customerPackage);

    // Log usage history for the cancellation event
    const usageHistoryNotes = adminNotes ? `Pacote cancelado pelo usuário. Observações: ${adminNotes}` : 'Pacote cancelado pelo usuário.';
    const usageHistory = this.usageHistoryRepository.create({
      customer_package_id: customerPackage.id,
      appointment_id: 0, // Using 0 as a sentinel for non-appointment specific package events
      service_date: new Date(), // Date of cancellation
      event_type: 'package_cancelled_by_user',
      status_at_usage: originalStatus as PackageUsageStatus, // Log the status before cancellation
      notes: usageHistoryNotes,
    });
    await this.usageHistoryRepository.save(usageHistory);

    return customerPackage;
  }

  async getAllActiveCustomerPackageIds(): Promise<number[]> {
    const activePackages = await this.repository.find({
      where: { is_deleted: false },
      select: ['id'],
    });
    return activePackages.map(cp => cp.id);
  }

  // Métodos para manipulação de recorrência, conclusão, no-show, resolução, etc., podem ser implementados conforme detalhamento do plano.
}