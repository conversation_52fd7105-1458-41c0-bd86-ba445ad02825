import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Customer } from './Customer';
import { Package } from './Package';
import { Pet } from './Pet';
import type { Appointment } from './Appointment';

export type CustomerPackageStatus =
  | 'pending_activation'
  | 'active'
  | 'on_hold_no_show'
  | 'on_hold_customer_request'
  | 'completed'
  | 'cancelled';

@Entity('customer_packages')
export class CustomerPackage {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  customer_id!: number;

  @ManyToOne(() => Customer)
  @JoinColumn({ name: 'customer_id' })
  customer?: Customer;

  @Column()
  package_id!: number;

  @ManyToOne(() => Package)
  @JoinColumn({ name: 'package_id' })
  package?: Package;

  @Column()
  pet_id!: number;

  @ManyToOne(() => Pet)
  @JoinColumn({ name: 'pet_id' })
  pet?: Pet;

  @Column({ type: 'varchar' })
  status!: CustomerPackageStatus;

  @Column()
  remaining_occurrences!: number;

  @Column({ type: 'datetime', nullable: true })
  activation_date!: Date | null;

  @Column({ type: 'integer', nullable: true })
  next_scheduled_appointment_id!: number | null;

  @ManyToOne('Appointment', undefined, { nullable: true })
  @JoinColumn({ name: 'next_scheduled_appointment_id' })
  nextScheduledAppointment?: Appointment | null;

  @Column({ type: 'datetime', nullable: true })
  current_cycle_first_scheduled_date!: Date | null;

  @Column({ type: 'datetime' })
  purchase_date!: Date;

  @Column({ type: 'datetime', nullable: true })
  expiry_date!: Date | null;

  @Column({ type: 'float' })
  price_paid!: number;

  @Column({ nullable: true, type: 'text' })
  notes!: string | null;

  @Column({ type: 'boolean', default: false })
  is_deleted!: boolean;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
}