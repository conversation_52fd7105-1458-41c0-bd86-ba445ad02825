import { Service } from './sales';
import { Appointment } from './appointments';
import { Pet } from './pets';

export interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  service_id: number;
  total_occurrences: number;
  frequency_type: 'weekly' | 'monthly' | 'custom_days';
  frequency_interval: number;
  is_active: boolean;
  created_at: string | Date;
  updated_at: string | Date;
}

export interface PackageFormData {
  name: string;
  description: string;
  price: number;
  service_id: number;
  total_occurrences: number;
  frequency_type: 'weekly' | 'monthly' | 'custom_days';
  frequency_interval: number;
  is_active: boolean;
}

export interface CustomerPackage {
  id: number;
  customer_id: number;
  package_id: number;
  pet_id: number;
  remaining_occurrences: number;
  purchase_date: string | Date;
  expiry_date: string | Date | null;
  price_paid: number | null;
  created_at: string | Date;
  updated_at: string | Date;
  status: 'pending_activation' | 'active' | 'on_hold_no_show' | 'on_hold_customer_request' | 'completed' | 'cancelled' | 'expired';
  package?: Package | null;
  next_scheduled_appointment_id?: number | null;
  nextScheduledAppointment?: Appointment | null;
  pet?: Pet | null;
}

export interface PackageUsageHistory {
  id: number;
  customer_package_id: number;
  service_id: number | null;
  pet_id: number | null;
  sale_id: number | null;
  sale_item_id: number | null;
  service_date: string | Date;
  notes: string | null;
  status_at_usage?: 'completed' | 'no_show' | 'cancelled' | string;
  event_type?: string;
  created_at: string | Date;
  service?: Service | null;
  pet?: {
    id: number;
    name: string;
    type: string;
  } | null;
  sale?: {
    id: number;
    sale_date: string | Date;
  } | null;
}