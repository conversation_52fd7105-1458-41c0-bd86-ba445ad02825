import { ipcMain, IpcMainInvokeEvent, dialog } from 'electron';
import { services } from '../database/services';
import { AppDataSource } from '../database/connection';
import { Package } from '../database/models/Package';
import { CustomerPackage } from '../database/models/CustomerPackage';
import { Sale } from '../database/models/Sale';
import { SaleItem } from '../database/models/SaleItem';
import { Customer } from '../database/models/Customer';
import { PackageUsageHistory } from '../database/models/PackageUsageHistory';
import { copyFile } from 'fs/promises';

// Utility function to safely extract error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

// Catch-all debug handler for IPC
ipcMain.on('any', (event, ...args) => {
  console.log('[DEBUG][backend] Received IPC message on channel "any":', args);
});

// Customer IPC Handlers
ipcMain.handle('customers:getAll', async () => {
  try {
    const customers = await services.customers.findAll();
    return { success: true, data: customers };
  } catch (error: unknown) {
    console.error('Error fetching customers:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customers:getInactive', async () => {
  try {
    const customers = await services.customers.findInactive();
    return { success: true, data: customers };
  } catch (error: unknown) {
    console.error('Error fetching inactive customers:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customers:getById', async (_: IpcMainInvokeEvent, id: number) => {
  try {
    const customer = await services.customers.findById(id);
    return { success: true, data: customer };
  } catch (error: unknown) {
    console.error(`Error fetching customer with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customers:search', async (_, search: string) => {
  try {
    const customers = await services.customers.findByEmailOrPhone(search);
    return { success: true, data: customers };
  } catch (error: unknown) {
    console.error('Error searching customers:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customers:create', async (_, customerData: any, forceCreate: boolean = false) => {
  try {
    const customer = await services.customers.create(customerData, forceCreate);
    return { success: true, data: customer };
  } catch (error: unknown) {
    console.error('Error creating customer:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customers:update', async (_, id: number, customerData: any) => {
  try {
    const customer = await services.customers.update(id, customerData);
    return { success: true, data: customer };
  } catch (error: unknown) {
    console.error(`Error updating customer with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customers:delete', async (_, id: number) => {
  try {
    const success = await services.customers.delete(id);
    return { success };
  } catch (error: unknown) {
    console.error(`Error deleting customer with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customers:reactivate', async (_, id: number) => {
  try {
    const customer = await services.customers.reactivate(id);
    return { success: true, data: customer };
  } catch (error: unknown) {
    console.error(`Error reactivating customer with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Pet IPC Handlers
ipcMain.handle('pets:getAll', async () => {
  try {
    const pets = await services.pets.findAll();
    return { success: true, data: pets };
  } catch (error: unknown) {
    console.error('Error fetching pets:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('pets:getById', async (_, id: number) => {
  try {
    const pet = await services.pets.findById(id);
    return { success: true, data: pet };
  } catch (error: unknown) {
    console.error(`Error fetching pet with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('pets:getByCustomerId', async (_, customerId: number) => {
  try {
    const pets = await services.pets.findByCustomerId(customerId);
    return { success: true, data: pets };
  } catch (error: unknown) {
    console.error(`Error getting pets for customer ${customerId}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('pets:getByCustomerIdIncludingHidden', async (_, customerId: number) => {
  try {
    const pets = await services.pets.findByCustomerIdIncludingHidden(customerId);
    return { success: true, data: pets };
  } catch (error: unknown) {
    console.error(`Error getting pets for customer ${customerId}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('pets:create', async (_, petData: any) => {
  try {
    // Handle the case where we receive customer_id instead of customer object
    if (petData.customer_id && !petData.customer) {
      petData.customer = { id: petData.customer_id };
      delete petData.customer_id;
    }
    
    const pet = await services.pets.create(petData);
    return { success: true, data: pet };
  } catch (error: unknown) {
    console.error('Error creating pet:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('pets:update', async (_, id: number, petData: any) => {
  try {
    // Handle the case where we receive customer_id instead of customer object
    if (petData.customer_id && !petData.customer) {
      petData.customer = { id: petData.customer_id };
      delete petData.customer_id;
    }
    
    const pet = await services.pets.update(id, petData);
    return { success: true, data: pet };
  } catch (error: unknown) {
    console.error(`Error updating pet with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('pets:delete', async (_, id: number) => {
  try {
    const success = await services.pets.delete(id);
    return { success };
  } catch (error: unknown) {
    console.error(`Error deleting pet with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('pets:reactivate', async (_, id: number) => {
  try {
    const pet = await services.pets.reactivate(id);
    return { success: true, data: pet };
  } catch (error: unknown) {
    console.error(`Error reactivating pet with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Product IPC Handlers
ipcMain.handle('products:getAll', async () => {
  try {
    const products = await services.products.findAll();
    return { success: true, data: products };
  } catch (error: unknown) {
    console.error('Error fetching products:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('products:getById', async (_, id: number) => {
  try {
    const product = await services.products.findById(id);
    return { success: true, data: product };
  } catch (error: unknown) {
    console.error(`Error fetching product with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('products:getByCategory', async (_, category: string) => {
  try {
    const products = await services.products.findByCategory(category);
    return { success: true, data: products };
  } catch (error: unknown) {
    console.error(`Error fetching products in category ${category}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('products:getLowStock', async () => {
  try {
    const products = await services.products.findLowStock();
    return { success: true, data: products };
  } catch (error: unknown) {
    console.error('Error fetching low stock products:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('products:create', async (_, productData: any) => {
  try {
    const product = await services.products.create(productData);
    return { success: true, data: product };
  } catch (error: unknown) {
    console.error('Error creating product:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('products:update', async (_, id: number, productData: any) => {
  try {
    const product = await services.products.update(id, productData);
    return { success: true, data: product };
  } catch (error: unknown) {
    console.error(`Error updating product with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('products:updateStock', async (_, id: number, quantity: number) => {
  try {
    const product = await services.products.updateStock(id, quantity);
    return { success: true, data: product };
  } catch (error: unknown) {
    console.error(`Error updating stock for product with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('products:delete', async (_, id: number) => {
  try {
    const success = await services.products.delete(id);
    return { success };
  } catch (error: unknown) {
    console.error(`Error deleting product with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Service IPC Handlers
ipcMain.handle('services:getAll', async () => {
  try {
    const servicesList = await services.services.findAll();
    return { success: true, data: servicesList };
  } catch (error: unknown) {
    console.error('Error fetching services:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('services:getById', async (_, id: number) => {
  try {
    const service = await services.services.findById(id);
    return { success: true, data: service };
  } catch (error: unknown) {
    console.error(`Error fetching service with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('services:create', async (_, serviceData: any) => {
  try {
    const service = await services.services.create(serviceData);
    return { success: true, data: service };
  } catch (error: unknown) {
    console.error('Error creating service:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('services:update', async (_, id: number, serviceData: any) => {
  try {
    const service = await services.services.update(id, serviceData);
    return { success: true, data: service };
  } catch (error: unknown) {
    console.error(`Error updating service with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('services:delete', async (_, id: number) => {
  try {
    const success = await services.services.delete(id);
    return { success };
  } catch (error: unknown) {
    console.error(`Error deleting service with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Sales IPC Handlers
ipcMain.handle('sales:getAll', async () => {
  try {
    const sales = await services.sales.findAll();
    return { success: true, data: sales };
  } catch (error: unknown) {
    console.error('Error fetching sales:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:getById', async (_, id: number) => {
  try {
    const sale = await services.sales.findById(id);
    return { success: true, data: sale };
  } catch (error: unknown) {
    console.error(`Error fetching sale with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:getByCustomerId', async (_, customerId: number) => {
  try {
    const sales = await services.sales.findByCustomerId(customerId);
    return { success: true, data: sales };
  } catch (error: unknown) {
    console.error(`Error fetching sales for customer id ${customerId}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:getByDateRange', async (_, startDate: string, endDate: string) => {
  try {
    const sales = await services.sales.findByDateRange(new Date(startDate), new Date(endDate));
    return { success: true, data: sales };
  } catch (error: unknown) {
    console.error(`Error fetching sales in date range ${startDate} to ${endDate}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:getSaleItems', async (_, saleId: number) => {
  try {
    const items = await services.sales.getSaleItems(saleId);
    return { success: true, data: items };
  } catch (error: unknown) {
    console.error(`Error fetching items for sale id ${saleId}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:create', async (_, saleData: any, items: any[]) => {
  try {
    const sale = await services.sales.create(saleData, items);
    return { success: true, data: sale };
  } catch (error: unknown) {
    console.error('Error creating sale:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:delete', async (_, id: number, withRestocking: boolean = true) => {
  try {
    const success = await services.sales.delete(id, withRestocking);
    return { success };
  } catch (error: unknown) {
    console.error(`Error deleting sale with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:updateStatus', async (_, id: number, status: string) => {
  try {
    const sale = await services.sales.updateStatus(id, status);
    return { success: true, data: sale };
  } catch (error: unknown) {
    console.error(`Error updating status for sale with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('sales:getPendingByCustomer', async (_, customerId: number) => {
  console.log(`Fetching pending sales for customer ${customerId}`);
  try {
    const pendingSales = await services.sales.getPendingByCustomerId(customerId);
    return { success: true, data: pendingSales };
  } catch (error) {
    console.error('Error fetching pending sales by customer:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Appointment IPC Handlers
ipcMain.handle('appointments:getAll', async () => {
  try {
    const appointments = await services.appointments.findAll();
    return { success: true, data: appointments };
  } catch (error: unknown) {
    console.error('Error fetching appointments:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:getById', async (_, id: number) => {
  try {
    const appointment = await services.appointments.findById(id);
    return { success: true, data: appointment };
  } catch (error: unknown) {
    console.error(`Error fetching appointment with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:getByCustomerId', async (_, customerId: number) => {
  try {
    const appointments = await services.appointments.findByCustomerId(customerId);
    return { success: true, data: appointments };
  } catch (error: unknown) {
    console.error(`Error fetching appointments for customer id ${customerId}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:getByPetId', async (_, petId: number) => {
  try {
    const appointments = await services.appointments.findByPetId(petId);
    return { success: true, data: appointments };
  } catch (error: unknown) {
    console.error(`Error fetching appointments for pet id ${petId}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:getUpcoming', async () => {
  try {
    const appointments = await services.appointments.findUpcoming();
    return { success: true, data: appointments };
  } catch (error: unknown) {
    console.error('Error fetching upcoming appointments:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:getByDateRange', async (_, startDate: string, endDate: string) => {
  try {
    const appointments = await services.appointments.findByDateRange(
      new Date(startDate), 
      new Date(endDate)
    );
    return { success: true, data: appointments };
  } catch (error: unknown) {
    console.error(`Error fetching appointments in date range ${startDate} to ${endDate}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:create', async (_, appointmentData: any) => {
  try {
    // Handle the case where we receive IDs instead of relationship objects
    if (appointmentData.customer_id && !appointmentData.customer) {
      appointmentData.customer = { id: appointmentData.customer_id };
      delete appointmentData.customer_id;
    }
    
    if (appointmentData.pet_id && !appointmentData.pet) {
      appointmentData.pet = { id: appointmentData.pet_id };
      delete appointmentData.pet_id;
    }
    
    if (appointmentData.service_id && !appointmentData.service) {
      appointmentData.service = { id: appointmentData.service_id };
      delete appointmentData.service_id;
    }
    
    const appointment = await services.appointments.create(appointmentData);
    return { success: true, data: appointment };
  } catch (error: unknown) {
    console.error('Error creating appointment:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:update', async (_, id: number, appointmentData: any) => {
  try {
    // Handle the case where we receive IDs instead of relationship objects
    if (appointmentData.customer_id && !appointmentData.customer) {
      appointmentData.customer = { id: appointmentData.customer_id };
      delete appointmentData.customer_id;
    }
    
    if (appointmentData.pet_id && !appointmentData.pet) {
      appointmentData.pet = { id: appointmentData.pet_id };
      delete appointmentData.pet_id;
    }
    
    if (appointmentData.service_id && !appointmentData.service) {
      appointmentData.service = { id: appointmentData.service_id };
      delete appointmentData.service_id;
    }
    
    const appointment = await services.appointments.update(id, appointmentData);
    return { success: true, data: appointment };
  } catch (error: unknown) {
    console.error(`Error updating appointment with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:updateStatus', async (_, id: number, status: string) => {
  try {
    const appointment = await services.appointments.updateStatus(id, status);
    return { success: true, data: appointment };
  } catch (error: unknown) {
    console.error(`Error updating status for appointment with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('appointments:delete', async (_, id: number) => {
  try {
    const success = await services.appointments.delete(id);
    return { success };
  } catch (error: unknown) {
    console.error(`Error deleting appointment with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Pet Services IPC Handlers
ipcMain.handle('petServices:getAll', async () => {
  try {
    const petServices = await services.petServices.findAll();
    return { success: true, data: petServices };
  } catch (error: unknown) {
    console.error('Error fetching pet services:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('petServices:getById', async (_: IpcMainInvokeEvent, id: number) => {
  try {
    const petService = await services.petServices.findById(id);
    return { success: true, data: petService };
  } catch (error: unknown) {
    console.error(`Error fetching pet service with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('petServices:getByPetId', async (_: IpcMainInvokeEvent, petId: number) => {
  try {
    const petServices = await services.petServices.findByPetId(petId);
    return { success: true, data: petServices };
  } catch (error: unknown) {
    console.error(`Error fetching pet services for pet id ${petId}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('petServices:create', async (_: IpcMainInvokeEvent, petServiceData: any) => {
  try {
    // Convert date string to Date object if needed
    if (typeof petServiceData.service_date === 'string') {
      petServiceData.service_date = new Date(petServiceData.service_date);
    }
    
    const petService = await services.petServices.create(petServiceData);
    return { success: true, data: petService };
  } catch (error: unknown) {
    console.error('Error creating pet service:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('petServices:update', async (_: IpcMainInvokeEvent, id: number, petServiceData: any) => {
  try {
    // Convert date string to Date object if needed
    if (typeof petServiceData.service_date === 'string') {
      petServiceData.service_date = new Date(petServiceData.service_date);
    }
    
    const petService = await services.petServices.update(id, petServiceData);
    return { success: true, data: petService };
  } catch (error: unknown) {
    console.error(`Error updating pet service with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('petServices:delete', async (_: IpcMainInvokeEvent, id: number) => {
  try {
    const success = await services.petServices.delete(id);
    return { success };
  } catch (error: unknown) {
    console.error(`Error deleting pet service with id ${id}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Package CRUD operations
ipcMain.handle('packages:getAll', async () => {
  try {
    const packageRepository = AppDataSource.getRepository(Package);
    const packages = await packageRepository.find();
    return { success: true, data: packages };
  } catch (error) {
    console.error('Error in packages:getAll:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('packages:getById', async (_, id: number) => {
  try {
    const packageRepository = AppDataSource.getRepository(Package);
    const packageItem = await packageRepository.findOneBy({ id });
    return { success: true, data: packageItem };
  } catch (error) {
    console.error('Error in packages:getById:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('packages:create', async (_, packageData: any) => {
  try {
    const packageRepository = AppDataSource.getRepository(Package);
    const newPackage = packageRepository.create(packageData);
    const savedPackage = await packageRepository.save(newPackage);
    return { success: true, data: savedPackage };
  } catch (error) {
    console.error('Error in packages:create:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('packages:update', async (_, id: number, packageData: any) => {
  try {
    const packageRepository = AppDataSource.getRepository(Package);
    await packageRepository.update(id, packageData);
    const updatedPackage = await packageRepository.findOneBy({ id });
    return { success: true, data: updatedPackage };
  } catch (error) {
    console.error('Error in packages:update:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('packages:delete', async (_, id: number) => {
  try {
    // A chamada ao serviço agora lida com a lógica de "soft delete"
    const success = await services.packages.delete(id);
    if (!success) {
      return { success: false, error: 'Pacote não encontrado ou falha ao desativar' };
    }
    return { success: true };
  } catch (error) {
    console.error('Error in packages:delete:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// PackageService IPC Handlers
ipcMain.handle('packages:getAllActive', async () => {
  try {
    const packages = await services.packages.getAllActivePackages();
    return { success: true, data: packages };
  } catch (error) {
    console.error('Error fetching active packages:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// CustomerPackageService IPC Handlers
ipcMain.handle('customerPackages:assign', async (_, customerId: number, packageId: number, petId: number, purchaseDate: string, notes?: string, expiryDate?: string | null) => {
  try {
    console.log('[customerPackages:assign] called with:', { customerId, packageId, petId, purchaseDate, notes, expiryDate });
    const customerPackage = await services.customerPackages.assignPackageToCustomer(
      customerId,
      packageId,
      petId,
      new Date(purchaseDate),
      notes,
      expiryDate ? new Date(expiryDate) : null
    );
    return { success: true, data: customerPackage };
  } catch (error) {
    console.error('[customerPackages:assign] ERROR:', error);
    if (error instanceof Error) {
      console.error('[customerPackages:assign] STACK:', error.stack);
    }
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:activate', async (_, customerPackageId: number, firstAppointmentDate: string) => {
  try {
    const result = await services.customerPackages.activateCustomerPackage(customerPackageId, new Date(firstAppointmentDate));
    return { success: true, data: result };
  } catch (error) {
    console.error('Error activating customer package:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:getForCustomer', async (_, customerId: number) => {
  try {
    const packages = await services.customerPackages.getCustomerPackages(customerId);
    return { success: true, data: packages };
  } catch (error) {
    console.error('Error fetching customer packages:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:getUpcomingAppointments', async () => {
  try {
    const appointments = await services.customerPackages.getUpcomingPackageAppointments();
    return { success: true, data: appointments };
  } catch (error) {
    console.error('Error fetching upcoming package appointments:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:getOnHold', async () => {
  try {
    const packages = await services.customerPackages.getPackagesOnHold();
    return { success: true, data: packages };
  } catch (error) {
    console.error('Error fetching packages on hold:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:resolveNoShowReschedule', async (_event, customerPackageId: number, newAppointmentDateTime: string, adminNotes?: string) => {
  try {
    const result = await services.customerPackages.resolveNoShowWithReschedule(customerPackageId, new Date(newAppointmentDateTime), adminNotes);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error in customerPackages:resolveNoShowReschedule:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:resolveNoShowByCancelling', async (_event, customerPackageId: number, adminNotes?: string) => {
  try {
    const result = await services.customerPackages.resolveNoShowByCancelling(customerPackageId, adminNotes);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error in customerPackages:resolveNoShowByCancelling:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:getByPurchaseDateRange', async (_, startDate: string, endDate: string) => {
  try {
    const customerPackages = await services.customerPackages.getPackagesByPurchaseDateRange(
      new Date(startDate),
      new Date(endDate)
    );
    return { success: true, data: customerPackages };
  } catch (error: unknown) {
    console.error(`Error fetching customer packages in purchase date range ${startDate} to ${endDate}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:addUsageHistory', async (_event, customerPackageId, appointmentId, serviceDate, status = 'completed', notes = null) => {
  console.log('[DEBUG][backend] addUsageHistory called:', { customerPackageId, appointmentId, serviceDate, status, notes });
  try {
    const repo = AppDataSource.getRepository(PackageUsageHistory);
    const existing = await repo.findOneBy({ customer_package_id: customerPackageId, appointment_id: appointmentId });
    if (existing) {
      console.log('[DEBUG][backend] Usage history already exists for this appointment/package.');
      return { success: false, error: 'Histórico de uso já registrado para este agendamento.' };
    }
    const entry = repo.create({
      customer_package_id: customerPackageId,
      appointment_id: appointmentId,
      service_date: new Date(serviceDate),
      status_at_usage: status,
      created_at: new Date(),
      notes: notes
    });
    await repo.save(entry);
    console.log('[DEBUG][backend] Usage history saved:', entry);
    return { success: true, data: entry };
  } catch (error) {
    console.error('[DEBUG][backend] Error in addUsageHistory:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:getUsageHistory', async (_event, customerPackageId) => {
  try {
    const repo = AppDataSource.getRepository(PackageUsageHistory);
    const history = await repo.find({ where: { customer_package_id: customerPackageId }, order: { service_date: 'ASC' } });
    return { success: true, data: history };
  } catch (error) {
    console.error('Error in customerPackages:getUsageHistory:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:cancelByUser', async (_event, customerPackageId: number, adminNotes?: string) => {
  try {
    const result = await services.customerPackages.cancelCustomerPackageByUser(customerPackageId, adminNotes);
    if (!result) {
      return { success: false, error: 'Falha ao cancelar o pacote do cliente ou pacote não encontrado.' };
    }
    // Check if the package status is indeed cancelled, or if it was returned because it was not in a cancellable state
    if (result.status !== 'cancelled') {
        return { success: false, error: `Não foi possível cancelar o pacote. Estado atual: ${result.status}` }
    }
    return { success: true, data: result };
  } catch (error) {
    console.error('Error in customerPackages:cancelByUser:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

ipcMain.handle('customerPackages:getAllActiveIds', async () => {
  try {
    const ids = await services.customerPackages.getAllActiveCustomerPackageIds();
    return { success: true, data: ids };
  } catch (error) {
    console.error('Error in customerPackages:getAllActiveIds:', error);
    return { success: false, error: getErrorMessage(error) };
  }
});

// Após todos os handlers existentes, antes do console.log final, adiciono handlers de backup
ipcMain.handle('db:exportDatabase', async () => {
  try {
    const dbPath = (AppDataSource.options as any).database as string;
    const { filePath } = await dialog.showSaveDialog({
      title: 'Salvar Backup do Banco de Dados',
      defaultPath: 'petshop-backup.sqlite',
      filters: [{ name: 'SQLite', extensions: ['sqlite', 'db'] }]
    });
    if (filePath) {
      await copyFile(dbPath, filePath);
      return { success: true };
    } else {
      return { success: false, error: 'Exportação cancelada pelo usuário' };
    }
  } catch (error) {
    console.error('Erro no exportDatabase:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('db:importDatabase', async (_: IpcMainInvokeEvent, sourcePath: string) => {
  try {
    const dbPath = (AppDataSource.options as any).database as string;
    const { response } = await dialog.showMessageBox({
      type: 'warning',
      buttons: ['Sim', 'Não'],
      defaultId: 1,
      cancelId: 1,
      title: 'Confirmar Importação',
      message: 'Isso irá substituir o banco de dados atual. Deseja continuar?'
    });
    if (response === 0) {
      await copyFile(sourcePath, dbPath);
      return { success: true };
    } else {
      return { success: false, error: 'Importação cancelada pelo usuário' };
    }
  } catch (error) {
    console.error('Erro no importDatabase:', error);
    return { success: false, error: (error as Error).message };
  }
});

console.log('[DEBUG][backend] Registered IPC handlers:', ipcMain.eventNames()); 