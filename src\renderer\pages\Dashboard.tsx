import React, { useState, useEffect, useMemo, useCallback, useReducer, useRef, memo } from 'react';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import ButtonGroup from '@mui/material/ButtonGroup';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Fade from '@mui/material/Fade';
import Zoom from '@mui/material/Zoom';
import { useTheme } from '@mui/material/styles';
import { alpha } from '@mui/material';
import {
  Pets as PetsIcon,
  AttachMoney as MoneyIcon,
  Warning as WarningIcon,
  EventAvailable as AppointmentIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Inventory as InventoryIcon,
  DateRange as DateRangeIcon,
  Today as TodayIcon,
  ArrowForward as ArrowForwardIcon,
  Dashboard as DashboardIcon,
  ErrorOutline as ErrorOutlineIcon,
} from '@mui/icons-material';
import { StatCard } from '@components/Dashboard/StatCard';
import { AppointmentList } from '../components/Dashboard/AppointmentList';
import { Appointment as AppointmentType } from '../types/dashboard';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { Line, Doughnut, Bar } from 'react-chartjs-2';
import { useAppointments } from '../hooks/useAppointments';
import { useSales } from '../hooks/useSales';
import { useProducts } from '../hooks/useProducts';
import { usePets } from '../hooks/usePets';
import { useServices } from '../hooks/useServices';
import { AppointmentFormDialog } from '../components/Appointments/AppointmentFormDialog';
import { AppointmentFormData, Appointment as RendererAppointment } from '../types/appointments';
import { 
  getBrazilianNow, 
  toBrazilianTimezone, 
  formatBrazilianDate, 
  BRAZIL_TIMEZONE 
} from '../utils/dateUtils';
import { useNavigate } from 'react-router-dom';
import { Chip } from '@mui/material';
import { usePackageCompletionNotification } from '../utils/packageNotifications';
import { useCustomerPackages } from '../hooks/useCustomerPackages';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend
);

const revenueChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: true,
      text: 'Visão Geral da Receita',
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      titleColor: '#333',
      bodyColor: '#666',
      callbacks: {
        label: function(context: any) {
          let label = context.dataset.label || '';
          if (label) {
            label += ': ';
          }
          if (context.parsed.y !== null) {
            label += new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(context.parsed.y);
          }
          return label;
        }
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: function(value: any) {
          return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value);
        }
      }
    }
  },
  elements: {
    line: {
      tension: 0.4
    }
  }
};

const servicesDistributionOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right' as const,
    },
    title: {
      display: true,
      text: 'Distribuição de Serviços',
    }
  },
  cutout: '65%',
};

const stockStatusOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'top' as const,
      labels: {
        usePointStyle: true,
        filter: (legendItem: any) => {
          // Only show items from the labels array, not the dataset label
          return legendItem.text !== 'Quantidade';
        }
      }
    },
    title: {
      display: true,
      text: 'Status do Estoque',
    },
    tooltip: {
      callbacks: {
        title: function(context: any) {
          return context[0].label;
        }
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
    }
  }
};

interface ServiceDistribution {
  [key: string]: number;
}

// Add dialog state reducer type definitions
type DialogState = {
  appointmentForm: {
    isOpen: boolean;
    appointmentData: any | null;
  };
  deleteConfirmation: {
    isOpen: boolean;
    appointmentId: number | null;
  };
  createSale: {
    isOpen: boolean;
    appointmentData: any | null;
  };
};

type DialogAction = 
  | { type: 'OPEN_APPOINTMENT_FORM'; payload?: any }
  | { type: 'CLOSE_APPOINTMENT_FORM' }
  | { type: 'OPEN_DELETE_CONFIRMATION'; payload: number }
  | { type: 'CLOSE_DELETE_CONFIRMATION' }
  | { type: 'OPEN_CREATE_SALE'; payload: any }
  | { type: 'CLOSE_CREATE_SALE' };

// Memoize the StatCard component to prevent unnecessary re-renders
const MemoizedStatCard = memo(StatCard);

// Memoize the appointment list component
const MemoizedAppointmentList = memo(AppointmentList);

// Memoized chart components
const MemoizedLine = memo(Line);
const MemoizedDoughnut = memo(Doughnut);
const MemoizedBar = memo(Bar);

// Add this currency formatter helper outside the component to create a singleton
const currencyFormatter = new Intl.NumberFormat('pt-BR', { 
  style: 'currency', 
  currency: 'BRL' 
});

// Extracted formatAppointment function
const formatAppointment = (appt: any): AppointmentType => {
  const apptDate = toBrazilianTimezone(appt.appointment_date);
  
  // Format date in Brazilian timezone explicitly to avoid UTC conversion
  const year = apptDate.getFullYear();
  const month = String(apptDate.getMonth() + 1).padStart(2, '0');
  const day = String(apptDate.getDate()).padStart(2, '0');
  const formattedDate = `${year}-${month}-${day}`;
  
  // Get time in Brazilian format
  const formattedTime = apptDate.toLocaleTimeString('pt-BR', {
    hour: '2-digit', 
    minute: '2-digit', 
    timeZone: BRAZIL_TIMEZONE
  });
  
  return {
    id: appt.id,
    time: `${formattedDate} ${formattedTime}`,
    petName: appt.pet?.name || 'Unknown Pet',
    ownerName: appt.customer?.name || 'Unknown Owner',
    service: appt.service?.name || 'Unknown Service',
    status: appt.status,
    is_package_appointment: appt.is_package_appointment || false
  };
};

export const Dashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [timeView, setTimeView] = useState<'day' | 'week'>('day');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Add ref to track initial loading
  const initialLoadCompletedRef = useRef(false);

  // Consolidate dialog-related state with useReducer
  const initialDialogState: DialogState = {
    appointmentForm: {
      isOpen: false,
      appointmentData: null
    },
    deleteConfirmation: {
      isOpen: false,
      appointmentId: null
    },
    createSale: {
      isOpen: false,
      appointmentData: null
    }
  };

  const dialogReducer = (state: DialogState, action: DialogAction): DialogState => {
    switch (action.type) {
      case 'OPEN_APPOINTMENT_FORM':
        return {
          ...state,
          appointmentForm: {
            isOpen: true,
            appointmentData: action.payload || null
          }
        };
      case 'CLOSE_APPOINTMENT_FORM':
        return {
          ...state,
          appointmentForm: {
            isOpen: false,
            appointmentData: null
          }
        };
      case 'OPEN_DELETE_CONFIRMATION':
        return {
          ...state,
          deleteConfirmation: {
            isOpen: true,
            appointmentId: action.payload
          }
        };
      case 'CLOSE_DELETE_CONFIRMATION':
        return {
          ...state,
          deleteConfirmation: {
            isOpen: false,
            appointmentId: null
          }
        };
      case 'OPEN_CREATE_SALE':
        return {
          ...state,
          createSale: {
            isOpen: true,
            appointmentData: action.payload
          }
        };
      case 'CLOSE_CREATE_SALE':
        return {
          ...state,
          createSale: {
            isOpen: false,
            appointmentData: null
          }
        };
      default:
        return state;
    }
  };

  const [dialogState, dispatchDialog] = useReducer(dialogReducer, initialDialogState);
  
  // Add state for data, removing ones that will be computed with useMemo
  const [todayAppointments, setTodayAppointments] = useState<AppointmentType[]>([]);
  const [weekAppointments, setWeekAppointments] = useState<AppointmentType[]>([]);
  const [todayRevenue, setTodayRevenue] = useState<number>(0);
  const [weeklyRevenue, setWeeklyRevenue] = useState<number>(0);
  const [lowStockCount, setLowStockCount] = useState<number>(0);
  const [recentlyUpdatedAppointmentId, setRecentlyUpdatedAppointmentId] = useState<number | null>(null);
  
  // Raw data for charts - will be processed with useMemo
  const [salesChartData, setSalesChartData] = useState<{
    hourlyServiceData: number[];
    hourlyProductData: number[];
    hourlyPackageData: number[];
    dailyServiceData: number[];
    dailyProductData: number[];
    dailyPackageData: number[];
  }>({
    hourlyServiceData: new Array(13).fill(0),
    hourlyProductData: new Array(13).fill(0),
    hourlyPackageData: new Array(13).fill(0),
    dailyServiceData: new Array(7).fill(0),
    dailyProductData: new Array(7).fill(0),
    dailyPackageData: new Array(7).fill(0)
  });
  
  // Add state for customers, pets, and services
  const [allCustomers, setAllCustomers] = useState<any[]>([]);
  const [allPets, setAllPets] = useState<any[]>([]);
  const [allServices, setAllServices] = useState<any[]>([]);
  
  // Hooks for database data
  const { 
    appointments: allAppointments, 
    loading: loadingAppointments,
    getUpcomingAppointments,
    getAppointmentsByDateRange,
    getAppointmentById,
    updateAppointment,
    updateAppointmentStatus,
    deleteAppointment,
  } = useAppointments();
  
  const { 
    sales, 
    loading: loadingSales,
    getSalesByDateRange,
    getSaleItems
  } = useSales();
  
  const { 
    products, 
    loading: loadingProducts,
    getLowStockProducts
  } = useProducts();
  
  const {
    pets,
    loading: loadingPets
  } = usePets();
  
  const {
    services,
    loading: loadingServices
  } = useServices();
  
  const { addUsageHistory } = useCustomerPackages();
  
  // Loading state
  const isLoading = loadingAppointments || loadingSales || loadingProducts || loadingPets || loadingServices;
  
  // Compute chart data using useMemo instead of storing in state
  const revenueData = useMemo(() => {
    const hourlyLabels = ['6h', '7h', '8h', '9h', '10h', '11h', '12h', '13h', '14h', '15h', '16h', '17h', '18h'];
    const dailyLabels = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
    
    return {
      labels: timeView === 'day' ? hourlyLabels : dailyLabels,
      datasets: [
        {
          label: 'Serviços',
          data: timeView === 'day' ? salesChartData.hourlyServiceData : salesChartData.dailyServiceData,
          borderColor: '#9c27b0',
          backgroundColor: 'rgba(156, 39, 176, 0.2)',
          tension: 0.4,
        },
        {
          label: 'Produtos',
          data: timeView === 'day' ? salesChartData.hourlyProductData : salesChartData.dailyProductData,
          borderColor: '#2196f3',
          backgroundColor: 'rgba(33, 150, 243, 0.2)',
          tension: 0.4,
        },
        {
          label: 'Pacotes',
          data: timeView === 'day' ? salesChartData.hourlyPackageData : salesChartData.dailyPackageData,
          borderColor: '#4caf50',
          backgroundColor: 'rgba(76, 175, 80, 0.2)',
          tension: 0.4,
        },
      ],
    };
  }, [timeView, salesChartData]);

  const servicesDistributionData = useMemo(() => {
    if (!services || services.length === 0) {
      return {
        labels: ['Sem Dados'],
        datasets: [
          {
            data: [1],
            backgroundColor: ['#e0e0e0'],
            borderWidth: 1,
          },
        ],
      };
    }
    
    // Prepare service categories and count their appointments
    const serviceStats: ServiceDistribution = {};
    for (const service of services) {
      serviceStats[service.name] = 0;
    }
    
    // Count appointments by service type
    for (const appt of allAppointments) {
      if (appt.service && appt.service.name) {
        serviceStats[appt.service.name] = (serviceStats[appt.service.name] || 0) + 1;
      }
    }
    
    // Prepare chart data
    const labels = Object.keys(serviceStats);
    const data = Object.values(serviceStats);
    
    if (labels.length === 0 || data.every(val => val === 0)) {
      return {
        labels: ['Sem Agendamentos'],
        datasets: [
          {
            data: [1],
            backgroundColor: ['#e0e0e0'],
            borderWidth: 1,
          },
        ],
      };
    } 
    
    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor: [
            '#9c27b0',
            '#2196f3',
            '#ff9800',
            '#4caf50',
            '#f44336',
            '#795548',
            '#607d8b',
          ],
          borderWidth: 1,
        },
      ],
    };
  }, [services, allAppointments]);

  const stockStatusData = useMemo(() => {
    if (!products || products.length === 0) {
      return {
        labels: ['Sem Dados'],
        datasets: [
          {
            data: [1],
            backgroundColor: ['#e0e0e0'],
            borderWidth: 1,
          },
        ],
      };
    }
    
    let lowStock = 0;
    let optimal = 0;
    let overstocked = 0;
    
    for (const product of products) {
      if (product.stock_quantity <= product.min_stock_level) {
        lowStock++;
      } else if (product.stock_quantity >= product.min_stock_level * 3) {
        overstocked++;
      } else {
        optimal++;
      }
    }
    
    return {
      labels: ['Estoque Baixo', 'Ideal', 'Em Excesso'],
      datasets: [
        {
          label: 'Quantidade',
          data: [lowStock, optimal, overstocked],
          backgroundColor: [
            '#f44336',
            '#4caf50',
            '#ff9800',
          ],
          borderWidth: 1,
        },
      ],
    };
  }, [products]);

  // Define fetchData as a memoized function with updated logic for new state structure
  const fetchData = useCallback(async () => {
    try {
      setIsRefreshing(true);
      // Get today's date and time in Brazil timezone - call this once and reuse
      const today = getBrazilianNow();
      today.setHours(0, 0, 0, 0);
      
      // Today's end date
      const todayEnd = new Date(today);
      todayEnd.setHours(23, 59, 59, 999);
      
      // For appointments: Get dates for the upcoming week (next 7 days)
      const weekEnd = new Date(today);
      weekEnd.setDate(weekEnd.getDate() + 6); // next 7 days including today
      weekEnd.setHours(23, 59, 59, 999);
      
      // For revenue: Get the current week's beginning (Sunday or Monday, depending on locale)
      const currentWeekStart = new Date(today);
      const dayOfWeek = currentWeekStart.getDay(); // 0 = Sunday, 1 = Monday, etc.
      currentWeekStart.setDate(currentWeekStart.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1)); // Go back to the Monday of the current week
      currentWeekStart.setHours(0, 0, 0, 0);
      
      // Batch API calls to run in parallel using Promise.all
      const [
        todayApptsResult,
        weeklyApptsResult,
        todaySales,
        weeklySales,
        lowStockProducts,
        customersResponse,
        petsResponse,
        servicesResponse,
        todayPackagePurchasesResult,
        weeklyPackagePurchasesResult,
        activePackageIdsResult
      ] = await Promise.all([
        getAppointmentsByDateRange(today, todayEnd),
        getAppointmentsByDateRange(today, weekEnd),
        getSalesByDateRange(today, todayEnd),
        getSalesByDateRange(currentWeekStart, todayEnd),
        getLowStockProducts(),
        window.electronAPI.invoke('customers:getAll'),
        window.electronAPI.invoke('pets:getAll'),
        window.electronAPI.invoke('services:getAll'),
        window.electronAPI.invoke('customerPackages:getByPurchaseDateRange', today.toISOString(), todayEnd.toISOString()),
        window.electronAPI.invoke('customerPackages:getByPurchaseDateRange', currentWeekStart.toISOString(), todayEnd.toISOString()),
        window.electronAPI.invoke('customerPackages:getAllActiveIds')
      ]);
      
      // Process appointment data in batches to avoid blocking the main thread
      // for large datasets
      const formattedTodayAppts = todayApptsResult
        .filter(appt => !recentlyUpdatedAppointmentId || appt.id !== recentlyUpdatedAppointmentId)
        .map(formatAppointment);
      const formattedWeekAppts = weeklyApptsResult
        .filter(appt => !recentlyUpdatedAppointmentId || appt.id !== recentlyUpdatedAppointmentId)
        .map(formatAppointment);
      
      // Calculate revenue totals
      let todayRev = 0;
      let weeklyRev = 0;
      
      // Use reduce for more efficient calculation instead of looping
      todayRev = todaySales
        .filter(sale => sale.status === 'paid')
        .reduce((total, sale) => total + sale.total_amount, 0);
      
      weeklyRev = weeklySales
        .filter(sale => sale.status === 'paid')
        .reduce((total, sale) => total + sale.total_amount, 0);

      // Add revenue from package purchases
      if (todayPackagePurchasesResult && todayPackagePurchasesResult.success && Array.isArray(todayPackagePurchasesResult.data)) {
        todayPackagePurchasesResult.data.forEach((cp: any) => {
          if (cp.package && typeof cp.package.price === 'number') {
            todayRev += cp.package.price;
          }
        });
      }

      if (weeklyPackagePurchasesResult && weeklyPackagePurchasesResult.success && Array.isArray(weeklyPackagePurchasesResult.data)) {
        weeklyPackagePurchasesResult.data.forEach((cp: any) => {
          if (cp.package && typeof cp.package.price === 'number') {
            weeklyRev += cp.package.price;
          }
        });
      }
      
      // Process customer, pet, and service data - create simple mapping functions
      // to transform API data into the format needed for state
      const formatCustomer = (customer: any) => ({
        id: customer.id,
        name: customer.name
      });
      
      const formatPet = (pet: any) => ({
        id: pet.id,
        name: pet.name,
        type: pet.type || 'Unknown',
        customer_id: pet.customer?.id || pet.customer_id || 0
      });
      
      const formatService = (service: any) => ({
        id: service.id,
        name: service.name,
        duration_minutes: service.duration_minutes || 0,
        price: service.price || 0
      });
      
      let formattedCustomers: any[] = [];
      let formattedPets: any[] = [];
      let formattedServices: any[] = [];
      
      if (customersResponse.success) {
        formattedCustomers = customersResponse.data.map(formatCustomer);
      }
      
      if (petsResponse.success) {
        formattedPets = petsResponse.data.map(formatPet);
      }
      
      if (servicesResponse.success) {
        formattedServices = servicesResponse.data.map(formatService);
      }
      
      // Generate hourly or daily labels for charts
      const hourlyLabels = ['6h', '7h', '8h', '9h', '10h', '11h', '12h', '13h', '14h', '15h', '16h', '17h', '18h'];
      const hourlyServiceData = new Array(13).fill(0);
      const hourlyProductData = new Array(13).fill(0);
      const hourlyPackageData = new Array(13).fill(0);
      
      const dailyLabels = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
      const dailyServiceData = new Array(7).fill(0);
      const dailyProductData = new Array(7).fill(0);
      const dailyPackageData = new Array(7).fill(0);
      
      // Create an object to store already fetched items to avoid duplicate API calls
      const saleItemsCache: { [key: number]: any[] } = {};
      
      // Get a list of all unique sale IDs we need to fetch
      const allSaleIds = [
        ...todaySales.filter(sale => sale.status === 'paid').map(sale => sale.id),
        ...weeklySales.filter(sale => sale.status === 'paid').map(sale => sale.id)
      ];
      // Remove duplicates (sales that appear in both today and weekly)
      const uniqueSaleIds = [...new Set(allSaleIds)];
      
      // Batch fetch all sale items at once
      const saleItemsPromises = uniqueSaleIds.map(saleId => 
        getSaleItems(saleId).then(items => ({ saleId, items }))
      );
      
      const saleItemsResults = await Promise.all(saleItemsPromises);
      
      // Populate cache with results
      saleItemsResults.forEach(result => {
        saleItemsCache[result.saleId] = result.items;
      });
      
      // Process sales data more efficiently
      const processSales = (
        sales: any[], 
        hourServiceDataArray: number[],
        hourProductDataArray: number[],
        dayServiceDataArray: number[],
        dayProductDataArray: number[],
        isToday: boolean,
        activeCustomerPackageIds: number[]
      ) => {
        for (const sale of sales) {
          if (sale.status !== 'paid') continue;
          
          const items = saleItemsCache[sale.id] || [];
          const saleDate = new Date(sale.sale_date);
          
          for (const item of items) {
            // Check if item is part of an active customer package
            if (item.customer_package_id && !activeCustomerPackageIds.includes(item.customer_package_id)) {
              continue; // Skip this item as its package is soft-deleted
            }

            const itemValue = Number(item.price_per_unit) * Number(item.quantity || 1);
            
            if (isToday) {
              // Process today's sales by hour
              const saleHour = saleDate.getHours();
              if (saleHour >= 6 && saleHour <= 18) {
                const hourIndex = saleHour - 6;
                if (item.service) {
                  hourServiceDataArray[hourIndex] += itemValue;
                } else if (item.product) {
                  hourProductDataArray[hourIndex] += itemValue;
                }
              }
            } else {
              // Process weekly sales by day
              const dayIndex = (saleDate.getDay() + 6) % 7; // Monday is 0, Sunday is 6
              if (item.service) {
                dayServiceDataArray[dayIndex] += itemValue;
              } else if (item.product) {
                dayProductDataArray[dayIndex] += itemValue;
              }
            }
          }
        }
      };
      
      // Process sales data - no more async calls needed as we have all items in cache
      // Extract active package IDs from the result
      const activePackageIds = (activePackageIdsResult && activePackageIdsResult.success && Array.isArray(activePackageIdsResult.data)) 
        ? activePackageIdsResult.data 
        : [];

      processSales(todaySales, hourlyServiceData, hourlyProductData, dailyServiceData, dailyProductData, true, activePackageIds);
      processSales(weeklySales, hourlyServiceData, hourlyProductData, dailyServiceData, dailyProductData, false, activePackageIds);
      
      // Add package purchase revenue to chart data
      if (todayPackagePurchasesResult && todayPackagePurchasesResult.success && Array.isArray(todayPackagePurchasesResult.data)) {
        todayPackagePurchasesResult.data.forEach((cp: any) => {
          if (cp.package && typeof cp.package.price === 'number' && cp.purchase_date) {
            const purchaseDate = toBrazilianTimezone(cp.purchase_date);
            const purchaseHour = purchaseDate.getHours();
            if (purchaseHour >= 6 && purchaseHour <= 18) {
              const hourIndex = purchaseHour - 6;
              hourlyPackageData[hourIndex] += cp.package.price;
            }
          }
        });
      }

      if (weeklyPackagePurchasesResult && weeklyPackagePurchasesResult.success && Array.isArray(weeklyPackagePurchasesResult.data)) {
        weeklyPackagePurchasesResult.data.forEach((cp: any) => {
          if (cp.package && typeof cp.package.price === 'number' && cp.purchase_date) {
            const purchaseDate = toBrazilianTimezone(cp.purchase_date);
            // Ensure purchaseDate is within the current week view (currentWeekStart to todayEnd)
            if (purchaseDate >= currentWeekStart && purchaseDate <= todayEnd) {
              const dayIndex = (purchaseDate.getDay() + 6) % 7; // Monday is 0, Sunday is 6
              dailyPackageData[dayIndex] += cp.package.price;
            }
          }
        });
      }
      
      // Update state with new data format
      setSalesChartData({
        hourlyServiceData,
        hourlyProductData,
        hourlyPackageData,
        dailyServiceData,
        dailyProductData,
        dailyPackageData
      });
      
      // Update other state values
      setTodayRevenue(todayRev);
      setWeeklyRevenue(weeklyRev);
      setLowStockCount(lowStockProducts.length);
      setAllCustomers(formattedCustomers);
      setAllPets(formattedPets);
      setAllServices(formattedServices);

      setTodayAppointments(currentTodayAppts => {
        if (recentlyUpdatedAppointmentId && timeView === 'day') {
          const locallyUpdatedItem = currentTodayAppts.find(appt => appt.id === recentlyUpdatedAppointmentId);
          if (locallyUpdatedItem) {
            return [...formattedTodayAppts, locallyUpdatedItem];
          }
        }
        return formattedTodayAppts;
      });
  
      setWeekAppointments(currentWeekAppts => {
        if (recentlyUpdatedAppointmentId && timeView === 'week') {
          const locallyUpdatedItem = currentWeekAppts.find(appt => appt.id === recentlyUpdatedAppointmentId);
          if (locallyUpdatedItem) {
            return [...formattedWeekAppts, locallyUpdatedItem];
          }
        }
        return formattedWeekAppts;
      });
      
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setIsRefreshing(false);
      setRecentlyUpdatedAppointmentId(null); // Reset the ID after fetch is complete
    }
  }, [
    timeView, 
    getAppointmentsByDateRange, 
    getSalesByDateRange, 
    getSaleItems, 
    getLowStockProducts,
    recentlyUpdatedAppointmentId // todayAppointments and weekAppointments removed
  ]);
  
  // Handler for manual refresh
  const handleRefresh = useCallback(() => {
    fetchData();
  }, [fetchData]);
  
  // Initial data loading - runs only once after hooks are ready
  useEffect(() => {
    if (!isLoading && !initialLoadCompletedRef.current) {
      fetchData();
      initialLoadCompletedRef.current = true;
    }
  }, [fetchData, isLoading]);
  
  // Fetch data when timeView changes
  useEffect(() => {
    if (initialLoadCompletedRef.current) { // Skip if initial load hasn't completed
      fetchData();
    }
  }, [timeView, fetchData]);
  
  // Current display data
  const displayAppointments = useMemo(() => {
    return timeView === 'day' ? todayAppointments : weekAppointments;
  }, [timeView, todayAppointments, weekAppointments]);

  // Handle edit appointment - updated to use reducer
  const handleEditAppointment = useCallback(async (appointment: AppointmentType) => {
    try {
      // Get the full appointment data from the database
      const dbAppointment = await getAppointmentById(appointment.id);
      
      if (dbAppointment) {
        // Format the appointment for the form
        const formattedAppointment = {
          id: dbAppointment.id,
          customer_id: dbAppointment.customer?.id || 0,
          pet_id: dbAppointment.pet?.id || 0,
          service_id: dbAppointment.service?.id || 0,
          appointment_date: dbAppointment.appointment_date,
          status: dbAppointment.status,
          notes: dbAppointment.notes || '',
          created_at: dbAppointment.created_at || new Date(),
          updated_at: dbAppointment.updated_at || new Date(),
          customer: dbAppointment.customer ? {
            id: dbAppointment.customer.id,
            name: dbAppointment.customer.name
          } : undefined,
          pet: dbAppointment.pet ? {
            id: dbAppointment.pet.id,
            name: dbAppointment.pet.name,
            type: dbAppointment.pet.type || 'Unknown',
            customer_id: dbAppointment.customer?.id || 0
          } : undefined,
          service: dbAppointment.service ? {
            id: dbAppointment.service.id,
            name: dbAppointment.service.name,
            duration_minutes: dbAppointment.service.duration_minutes || 0,
            price: dbAppointment.service.price || 0
          } : undefined
        };
        
        // Use reducer to update dialog state
        dispatchDialog({ 
          type: 'OPEN_APPOINTMENT_FORM', 
          payload: formattedAppointment 
        });
      } else {
        console.error("Could not find appointment with ID:", appointment.id);
      }
    } catch (error) {
      console.error("Error in handleEditAppointment:", error);
    }
  }, [getAppointmentById, dispatchDialog]);
  
  // Handle delete appointment - updated to use reducer
  const handleDeleteAppointment = useCallback((appointmentId: number) => {
    dispatchDialog({ 
      type: 'OPEN_DELETE_CONFIRMATION', 
      payload: appointmentId 
    });
  }, [dispatchDialog]);
  
  // Handle status change - updated to use reducer
  const { notifyPackageCompletion } = usePackageCompletionNotification();
  const handleStatusChange = useCallback(async (appointmentId: number, newStatus: string) => {
    console.log('[DEBUG] handleStatusChange called:', { appointmentId, newStatus });
    try {
      // First update the appointment status in the database
      await updateAppointmentStatus(appointmentId, newStatus);

      // Fetch the updated appointment immediately to get the freshest data
      const refreshedDbAppointment = await getAppointmentById(appointmentId);

      if (refreshedDbAppointment) {
        const formattedRefreshedAppointment = formatAppointment(refreshedDbAppointment); // Use extracted formatter

        // Update the local state immediately
        if (timeView === 'day') {
          setTodayAppointments(prev => 
            prev.map(appt => appt.id === appointmentId ? formattedRefreshedAppointment : appt)
          );
        } else {
          setWeekAppointments(prev => 
            prev.map(appt => appt.id === appointmentId ? formattedRefreshedAppointment : appt)
          );
        }

        // If the status is changed to completed, prompt to create a sale
        if (newStatus === 'completed') {
          // Use refreshedDbAppointment which has full details
          if (refreshedDbAppointment.service) {
            const fullAppointmentForSaleOrNotification = {
              id: refreshedDbAppointment.id,
              customer_id: refreshedDbAppointment.customer?.id || 0,
              pet_id: refreshedDbAppointment.pet?.id || 0,
              service_id: refreshedDbAppointment.service?.id || 0,
              appointment_date: typeof refreshedDbAppointment.appointment_date === 'string' 
                                ? refreshedDbAppointment.appointment_date 
                                : refreshedDbAppointment.appointment_date.toISOString(),
              status: refreshedDbAppointment.status as import('../types/appointments').AppointmentStatus,
              notes: refreshedDbAppointment.notes || null,
              created_at: refreshedDbAppointment.created_at,
              updated_at: refreshedDbAppointment.updated_at,
              customer: refreshedDbAppointment.customer,
              pet: refreshedDbAppointment.pet ? { ...refreshedDbAppointment.pet, customer_id: refreshedDbAppointment.customer?.id || 0 } : undefined,
              service: refreshedDbAppointment.service,
              is_package_appointment: refreshedDbAppointment.is_package_appointment,
              source_customer_package_id: refreshedDbAppointment.source_customer_package_id
            };

            // Log usage if this is a package appointment
            if (refreshedDbAppointment.is_package_appointment && refreshedDbAppointment.source_customer_package_id) {
              console.log('[DEBUG] Attempting to log package usage:', {
                appointment: refreshedDbAppointment,
                customerPackageId: refreshedDbAppointment.source_customer_package_id,
                appointmentId: refreshedDbAppointment.id,
                appointmentDate: fullAppointmentForSaleOrNotification.appointment_date,
                status: newStatus,
                notes: refreshedDbAppointment.notes
              });
              const result = await addUsageHistory(
                refreshedDbAppointment.source_customer_package_id,
                refreshedDbAppointment.id,
                fullAppointmentForSaleOrNotification.appointment_date,
                newStatus,
                refreshedDbAppointment.notes || null
              );
              console.log('[DEBUG] addUsageHistory result:', result);
            }
            // Notify about package completion/next schedule
            notifyPackageCompletion(fullAppointmentForSaleOrNotification);
            // Only show the sale dialog if NOT a package appointment
            if (!refreshedDbAppointment.is_package_appointment) {
              dispatchDialog({ 
                type: 'OPEN_CREATE_SALE', 
                payload: fullAppointmentForSaleOrNotification 
              });
            }
          }
        }
      } else {
        console.warn(`[DEBUG] Could not fetch refreshed appointment (ID: ${appointmentId}) after status update.`);
      }

      // Introduce a small delay before refreshing all dashboard data
      await new Promise(resolve => setTimeout(resolve, 100)); 

      setRecentlyUpdatedAppointmentId(appointmentId); // Set the ID before calling fetchData
      // Refresh all dashboard data (stats, charts, etc.)
      await fetchData(); 
    } catch (error) {
      console.error("Error updating appointment status:", error);
    }
  }, [updateAppointmentStatus, getAppointmentById, dispatchDialog, fetchData, notifyPackageCompletion, addUsageHistory, timeView]);
  
  // Handle sale creation after completion - updated to use reducer
  const handleCreateSale = useCallback(() => {
    if (dialogState.createSale.appointmentData && dialogState.createSale.appointmentData.service) {
      // Navigate to sales page with appointment data
      navigate('/sales', { 
        state: { 
          createFromAppointment: true,
          appointmentData: {
            customerId: dialogState.createSale.appointmentData.customer_id,
            serviceId: dialogState.createSale.appointmentData.service_id,
            serviceName: dialogState.createSale.appointmentData.service.name,
            servicePrice: dialogState.createSale.appointmentData.service.price,
            petId: dialogState.createSale.appointmentData.pet_id
          }
        } 
      });
    }
    dispatchDialog({ type: 'CLOSE_CREATE_SALE' });
  }, [dialogState.createSale.appointmentData, navigate, dispatchDialog]);

  const handleCancelCreateSale = useCallback(() => {
    dispatchDialog({ type: 'CLOSE_CREATE_SALE' });
  }, [dispatchDialog]);
  
  // Confirm delete appointment - updated to use reducer
  const confirmDeleteAppointment = useCallback(async () => {
    if (dialogState.deleteConfirmation.appointmentId) {
      try {
        await deleteAppointment(dialogState.deleteConfirmation.appointmentId);
        dispatchDialog({ type: 'CLOSE_DELETE_CONFIRMATION' });
        await fetchData(); // Refresh dashboard data
      } catch (error) {
        console.error("Error deleting appointment:", error);
      }
    }
  }, [dialogState.deleteConfirmation.appointmentId, deleteAppointment, dispatchDialog, fetchData]);
  
  // Handle save appointment (edit or create) - updated to use reducer
  const handleSaveAppointment = useCallback(async (formData: AppointmentFormData) => {
    try {
      if (dialogState.appointmentForm.appointmentData) {
        // Update existing appointment
        await updateAppointment(dialogState.appointmentForm.appointmentData.id, {
          customer_id: formData.customer_id,
          pet_id: formData.pet_id,
          service_id: formData.service_id,
          appointment_date: formData.appointment_date,
          status: formData.status,
          notes: formData.notes || ''
        } as any); // Using 'any' to work around type incompatibilities
      }
      
      dispatchDialog({ type: 'CLOSE_APPOINTMENT_FORM' });
      await fetchData(); // Refresh dashboard data
    } catch (error) {
      console.error("Error saving appointment:", error);
    }
  }, [dialogState.appointmentForm.appointmentData, updateAppointment, dispatchDialog, fetchData]);

  const handleViewToggle = useCallback((newView: 'day' | 'week') => {
    setTimeView(newView);
  }, []);

  // Memoize navigation handlers to prevent unnecessary re-renders
  const handleNavigateTo = useCallback((path: string, tabIndex?: number) => {
    if (tabIndex !== undefined) {
      navigate(path, { state: { tabIndex } });
    } else {
      navigate(path);
    }
  }, [navigate]);

  // Memoize chart rendering functions
  const renderRevenueChart = useMemo(() => {
    if (!revenueData) return (
      <Box sx={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <CircularProgress size={40} />
      </Box>
    );

    return (
      <Box sx={{ height: '300px', position: 'relative', p: 2 }}>
        <MemoizedLine options={revenueChartOptions} data={revenueData} />
      </Box>
    );
  }, [revenueData]);

  const renderServiceDistribution = useMemo(() => {
    if (!servicesDistributionData) return (
      <Box sx={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <CircularProgress size={40} />
      </Box>
    );

    return (
      <Box sx={{ height: '300px', position: 'relative', p: 2 }}>
        <MemoizedDoughnut options={servicesDistributionOptions} data={servicesDistributionData} />
      </Box>
    );
  }, [servicesDistributionData]);

  const renderStockStatus = useMemo(() => {
    if (!stockStatusData) return (
      <Box sx={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <CircularProgress size={40} />
      </Box>
    );

    return (
      <Box sx={{ height: '300px', position: 'relative', p: 2 }}>
        <MemoizedBar options={stockStatusOptions} data={stockStatusData} />
      </Box>
    );
  }, [stockStatusData]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 3,
          flexWrap: 'wrap',
          gap: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DashboardIcon sx={{ fontSize: 28, color: theme.palette.primary.main }} />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 'bold', 
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: 60,
                height: 4,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 2,
              }
            }}
          >
            Dashboard
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <ButtonGroup variant="outlined" size="small">
            <Button 
              onClick={() => handleViewToggle('day')} 
              variant={timeView === 'day' ? 'contained' : 'outlined'}
              startIcon={<TodayIcon />}
            >
              Hoje
            </Button>
            <Button 
              onClick={() => handleViewToggle('week')} 
              variant={timeView === 'week' ? 'contained' : 'outlined'}
              startIcon={<DateRangeIcon />}
            >
              Semana
            </Button>
          </ButtonGroup>
        </Box>
      </Box>

      <Fade in={true} timeout={500}>
        <Grid container spacing={3}>
          {/* Stats Cards row */}
          <Grid item xs={12} md={6} lg={3}>
            <Zoom in={true} style={{ transitionDelay: '100ms' }}>
              <Box>
                <MemoizedStatCard 
                  title="Receita"
                  value={currencyFormatter.format(timeView === 'day' ? todayRevenue : weeklyRevenue)}
                  icon={MoneyIcon}
                  color={theme.palette.success.main}
                  subtitle={timeView === 'day' ? "Receita de hoje" : "Receita da semana"}
                  onClick={() => handleNavigateTo('/reports')}
                />
              </Box>
            </Zoom>
          </Grid>
          
          <Grid item xs={12} md={6} lg={3}>
            <Zoom in={true} style={{ transitionDelay: '200ms' }}>
              <Box>
                <MemoizedStatCard 
                  title="Agendamentos"
                  value={timeView === 'day' ? todayAppointments.length : weekAppointments.length}
                  icon={AppointmentIcon}
                  color={theme.palette.primary.main}
                  subtitle={timeView === 'day' ? "Agendamentos para hoje" : "Agendamentos da semana"}
                  onClick={() => handleNavigateTo('/appointments')}
                />
              </Box>
            </Zoom>
          </Grid>
          
          <Grid item xs={12} md={6} lg={3}>
            <Zoom in={true} style={{ transitionDelay: '300ms' }}>
              <Box>
                <MemoizedStatCard 
                  title="Estoque baixo"
                  value={lowStockCount}
                  icon={WarningIcon}
                  color={theme.palette.warning.main}
                  subtitle="Produtos que precisam de reposição"
                  onClick={() => handleNavigateTo('/inventory')}
                />
              </Box>
            </Zoom>
          </Grid>
          
          <Grid item xs={12} md={6} lg={3}>
            <Zoom in={true} style={{ transitionDelay: '400ms' }}>
              <Box>
                <MemoizedStatCard 
                  title="Clientes Cadastrados"
                  value={allCustomers.length}
                  icon={PetsIcon}
                  color={theme.palette.info.main}
                  subtitle="Total de clientes no sistema"
                  onClick={() => handleNavigateTo('/customers')}
                />
              </Box>
            </Zoom>
          </Grid>

          {/* Charts row */}
          <Grid item xs={12} lg={8}>
            <Fade in={true} timeout={800}>
              <Paper 
                sx={{ 
                  p: 2, 
                  height: '100%',
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  transition: 'box-shadow 0.3s ease',
                  '&:hover': {
                    boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                  }
                }}
              >
                <Box 
                  sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    mb: 1
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TrendingUpIcon color="primary" />
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        position: 'relative',
                        pb: 1,
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          width: '40px',
                          height: '3px',
                          backgroundColor: theme.palette.primary.main,
                          borderRadius: '3px',
                        }
                      }}
                    >
                      Receita {timeView === 'day' ? 'Hoje' : 'da Semana'}
                    </Typography>
                  </Box>
                  <Button 
                    variant="text" 
                    color="primary" 
                    size="small" 
                    endIcon={<ArrowForwardIcon />}
                    onClick={() => handleNavigateTo('/reports')}
                    sx={{ 
                      '&:hover': { 
                        bgcolor: alpha(theme.palette.primary.main, 0.05),
                        transform: 'translateX(2px)',
                      },
                      transition: 'transform 0.2s ease',
                    }}
                  >
                    Ver relatórios
                  </Button>
                </Box>
                {renderRevenueChart}
              </Paper>
            </Fade>
          </Grid>
          
          <Grid item xs={12} lg={4}>
            <Fade in={true} timeout={800}>
              <Paper 
                sx={{ 
                  p: 2, 
                  height: '100%',
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  transition: 'box-shadow 0.3s ease',
                  '&:hover': {
                    boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                  }
                }}
              >
                <Box 
                  sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    mb: 1
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AssessmentIcon color="primary" />
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        position: 'relative',
                        pb: 1,
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          width: '40px',
                          height: '3px',
                          backgroundColor: theme.palette.primary.main,
                          borderRadius: '3px',
                        }
                      }}
                    >
                      Serviços Populares
                    </Typography>
                  </Box>
                  <Button 
                    variant="text" 
                    color="primary" 
                    size="small" 
                    endIcon={<ArrowForwardIcon />}
                    onClick={() => handleNavigateTo('/settings', 2)}
                    sx={{ 
                      '&:hover': { 
                        bgcolor: alpha(theme.palette.primary.main, 0.05),
                        transform: 'translateX(2px)',
                      },
                      transition: 'transform 0.2s ease',
                    }}
                  >
                    Ver serviços
                  </Button>
                </Box>
                {renderServiceDistribution}
              </Paper>
            </Fade>
          </Grid>

          {/* Appointments and Stock Status row */}
          <Grid item xs={12} md={7}>
            <Fade in={true} timeout={1000}>
              <Box sx={{ height: 480 }}>
                <MemoizedAppointmentList
                  appointments={displayAppointments}
                  title={timeView === 'day' ? 'Agendamentos de Hoje' : 'Agendamentos da Semana'}
                  maxHeight={400}
                  onEdit={handleEditAppointment}
                  onDelete={handleDeleteAppointment}
                  onStatusChange={handleStatusChange}
                />
              </Box>
            </Fade>
          </Grid>
          
          <Grid item xs={12} md={5}>
            <Fade in={true} timeout={1000}>
              <Paper 
                sx={{ 
                  p: 2, 
                  height: 480,
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  transition: 'box-shadow 0.3s ease',
                  '&:hover': {
                    boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                  },
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <Box 
                  sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    mb: 1
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <InventoryIcon color="primary" />
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        position: 'relative',
                        pb: 1,
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          width: '40px',
                          height: '3px',
                          backgroundColor: theme.palette.primary.main,
                          borderRadius: '3px',
                        }
                      }}
                    >
                      Status do Estoque
                    </Typography>
                  </Box>
                  <Button 
                    variant="text" 
                    color="primary" 
                    size="small" 
                    endIcon={<ArrowForwardIcon />}
                    onClick={() => handleNavigateTo('/inventory')}
                    sx={{ 
                      '&:hover': { 
                        bgcolor: alpha(theme.palette.primary.main, 0.05),
                        transform: 'translateX(2px)',
                      },
                      transition: 'transform 0.2s ease',
                    }}
                  >
                    Ver inventário
                  </Button>
                </Box>
                {renderStockStatus}
              </Paper>
            </Fade>
          </Grid>
        </Grid>
      </Fade>

      {/* Dialogs */}
      <AppointmentFormDialog
        open={dialogState.appointmentForm.isOpen}
        onClose={() => dispatchDialog({ type: 'CLOSE_APPOINTMENT_FORM' })}
        onSave={handleSaveAppointment}
        appointment={dialogState.appointmentForm.appointmentData}
        title={dialogState.appointmentForm.appointmentData ? 'Editar Consulta' : 'Adicionar Nova Consulta'}
        customers={allCustomers}
        pets={allPets}
        services={allServices}
      />

      <Dialog
        open={dialogState.deleteConfirmation.isOpen}
        onClose={() => dispatchDialog({ type: 'CLOSE_DELETE_CONFIRMATION' })}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Tem certeza que deseja excluir esta consulta? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => dispatchDialog({ type: 'CLOSE_DELETE_CONFIRMATION' })}>Cancelar</Button>
          <Button 
            onClick={confirmDeleteAppointment} 
            color="error"
          >
            Excluir
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create sale dialog */}
      <Dialog
        open={dialogState.createSale.isOpen}
        onClose={handleCancelCreateSale}
      >
        <DialogTitle>Criar Venda</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Deseja criar uma venda para esta consulta?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCreateSale} color="primary">
            Sim
          </Button>
          <Button onClick={handleCancelCreateSale} color="error">
            Não
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}; 