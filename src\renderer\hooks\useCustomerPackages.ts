import { useState, useCallback } from 'react';
import { CustomerPackage } from '../types/packages';
import { Appointment } from '../types/appointments';

interface UseCustomerPackagesResult {
  assignPackageToCustomer: (
    customerId: number,
    packageId: number,
    petId: number,
    purchaseDate: Date,
    notes?: string,
    expiryDate?: Date
  ) => Promise<CustomerPackage | null>;
  activateCustomerPackage: (customerPackageId: number, firstAppointmentDate: Date) => Promise<CustomerPackage | null>;
  getCustomerPackages: (customerId: number) => Promise<CustomerPackage[]>;
  getUpcomingPackageAppointments: () => Promise<Appointment[]>;
  getPackagesOnHold: () => Promise<CustomerPackage[]>;
  addUsageHistory: (customerPackageId: number, appointmentId: number, serviceDate: string, status?: string, notes?: string | null) => Promise<boolean>;
  getPackageUsageHistory: (customerPackageId: number) => Promise<any[]>;
  cancelCustomerPackage: (customerPackageId: number, adminNotes?: string) => Promise<CustomerPackage | null>;
  loading: boolean;
  error: string | null;
}

export const useCustomerPackages = (): UseCustomerPackagesResult => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const assignPackageToCustomer = useCallback(async (
    customerId: number,
    packageId: number,
    petId: number,
    purchaseDate: Date,
    notes?: string,
    expiryDate?: Date
  ): Promise<CustomerPackage | null> => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('customerPackages:assign', customerId, packageId, petId, purchaseDate.toISOString(), notes, expiryDate ? expiryDate.toISOString() : null);
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Falha ao atribuir pacote ao cliente');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const activateCustomerPackage = useCallback(async (
    customerPackageId: number,
    firstAppointmentDate: Date
  ): Promise<CustomerPackage | null> => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('customerPackages:activate', customerPackageId, firstAppointmentDate.toISOString());
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Falha ao ativar pacote do cliente');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getCustomerPackages = useCallback(async (customerId: number): Promise<CustomerPackage[]> => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('customerPackages:getForCustomer', customerId);
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Falha ao buscar pacotes do cliente');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  const getUpcomingPackageAppointments = useCallback(async (): Promise<Appointment[]> => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('customerPackages:getUpcomingAppointments');
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Falha ao buscar agendamentos futuros de pacotes');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  const getPackagesOnHold = useCallback(async (): Promise<CustomerPackage[]> => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('customerPackages:getOnHold');
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Falha ao buscar pacotes em espera');
      return [];
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  const addUsageHistory = useCallback(async (customerPackageId: number, appointmentId: number, serviceDate: string, status: string = 'completed', notes: string | null = null): Promise<boolean> => {
    try {
      const response = await window.electronAPI.invoke('customerPackages:addUsageHistory', customerPackageId, appointmentId, serviceDate, status, notes);
      if (!response.success) {
        console.error('[DEBUG] addUsageHistory backend error:', response.error);
      }
      return response.success;
    } catch (err) {
      setError('Falha ao registrar uso do pacote');
      return false;
    }
  }, []);

  const getPackageUsageHistory = useCallback(async (customerPackageId: number): Promise<any[]> => {
    try {
      const response = await window.electronAPI.invoke('customerPackages:getUsageHistory', customerPackageId);
      if (response.success) {
        return response.data;
      }
      setError(response.error || 'Falha ao buscar histórico de uso do pacote');
      return [];
    } catch (err) {
      setError('Falha ao buscar histórico de uso do pacote');
      return [];
    }
  }, []);

  const cancelCustomerPackage = useCallback(async (customerPackageId: number, adminNotes?: string): Promise<CustomerPackage | null> => {
    setLoading(true);
    setError(null);
    try {
      const response = await window.electronAPI.invoke('customerPackages:cancelByUser', customerPackageId, adminNotes);
      if (response.success) {
        return response.data; // Returns the updated/cancelled customer package
      }
      setError(response.error || 'Falha ao cancelar pacote do cliente.');
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    assignPackageToCustomer,
    activateCustomerPackage,
    getCustomerPackages,
    getUpcomingPackageAppointments,
    getPackagesOnHold,
    addUsageHistory,
    getPackageUsageHistory,
    cancelCustomerPackage,
    loading,
    error
  };
}; 