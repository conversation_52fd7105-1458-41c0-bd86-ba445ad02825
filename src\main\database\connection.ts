import { DataSource } from 'typeorm';
import path from 'path';
import bcrypt from 'bcryptjs';
import fs from 'fs/promises';
import { existsSync } from 'fs';
import { store } from '../store';
import { constants } from 'fs';

// Entities
import { User } from './models/User';
import { Customer } from './models/Customer';
import { Pet } from './models/Pet';
import { Product } from './models/Product';
import { Service } from './models/Service';
import { Sale } from './models/Sale';
import { SaleItem } from './models/SaleItem';
import { Appointment } from './models/Appointment';
import { PetService } from './models/PetService';
import { Package } from './models/Package';
import { CustomerPackage } from './models/CustomerPackage';
import { PackageUsageHistory } from './models/PackageUsageHistory';

// Migrations
import { AddIsDeletedToProducts1723000000000 } from './migrations/1_add_is_deleted_to_products';
import { AddPhotoUrlToPets1725000000000 } from './migrations/1725000000000-AddPhotoUrlToPets';
import { AddGenderToPets1725000000001 } from './migrations/1725000000001-AddGenderToPets';
import { AddFurTypeToPets1725000000002 } from './migrations/1725000000002-AddFurTypeToPets';
import { CreatePackageUsageHistory1726000000000 } from './migrations/1726000000000-CreatePackageUsageHistory';
import { AddCostPriceToProducts1730000000000 } from './migrations/1730000000000-AddCostPriceToProducts';

const isDev = process.env.NODE_ENV === 'development';

// Helper function to get database path
const getDatabasePath = async (): Promise<string> => {
  // Get the AppData path based on the platform
  const appData = process.env.APPDATA || (
    process.platform === 'darwin' 
      ? path.join(process.env.HOME || '', 'Library', 'Application Support')
      : path.join(process.env.HOME || '', '.config')
  );

  // Create the full path
  const dbPath = path.join(appData, 'pet-shop-management', 'petshop.db');
  
  // Ensure the directory exists
  const dbDir = path.dirname(dbPath);
  
  try {
    // Check if directory exists asynchronously
    try {
      await fs.access(dbDir);
    } catch {
      // Directory doesn't exist, create it
      await fs.mkdir(dbDir, { recursive: true });
    }
  } catch (error) {
    console.error(`[DB] Error while ensuring database directory exists:`, error);
    throw error; // Re-throw to handle it in the setup function
  }
  
  // Check if we can write to this location
  try {
    // Check if file exists
    const fileExists = existsSync(dbPath);
    
    if (fileExists) {
      // If file exists, check if we have write permission
      await fs.access(dbPath, constants.W_OK);
    } else {
      // If file doesn't exist, check if we can write to the directory
      await fs.access(dbDir, constants.W_OK);
    }
  } catch (error) {
    console.error(`[DB] Cannot write to database location:`, error);
    // Instead of throwing, we'll try to use a fallback location
    const fallbackPath = isDev 
      ? path.join(process.cwd(), 'petshop.db')
      : path.join(__dirname, '..', '..', 'petshop.db');
    
    return fallbackPath;
  }

  return dbPath;
};

// Initialize this asynchronously in setupDatabase
let dbPath: string;

export const AppDataSource = new DataSource({
  type: 'sqlite',
  database: '', // Will be set during initialization
  entities: [User, Customer, Pet, Product, Service, Sale, SaleItem, Appointment, PetService, Package, CustomerPackage, PackageUsageHistory],
  synchronize: true,
  logging: ['error'],
  migrations: [
    AddIsDeletedToProducts1723000000000, 
    AddPhotoUrlToPets1725000000000, 
    AddGenderToPets1725000000001,
    AddFurTypeToPets1725000000002,
    CreatePackageUsageHistory1726000000000,
    AddCostPriceToProducts1730000000000
  ],
});

// Function to create a default admin user if none exists
async function createDefaultAdminUser() {
  try {
    // Check if admin has already been created using electron-store
    const adminCreated = store.get('adminUserCreated');
    
    // If admin user was already created, skip the database check
    if (adminCreated) {
      return;
    }
    
    // If flag not set, perform database check
    const userRepository = AppDataSource.getRepository(User);
    const adminCount = await userRepository.count();
    
    if (adminCount === 0) {
      // Create default admin user
      const passwordHash = await bcrypt.hash('admin123', 10);
      
      const defaultAdmin = userRepository.create({
        username: 'admin',
        password_hash: passwordHash,
        role: 'admin',
        created_at: new Date(),
        last_login: new Date()
      });
      
      await userRepository.save(defaultAdmin);
      
      // Set the flag in electron-store to skip this check in future startups
      store.set('adminUserCreated', true);
    } else {
      // Admin exists but flag wasn't set, set it now
      store.set('adminUserCreated', true);
    }
  } catch (error) {
    console.error('Error creating default admin user:', error);
  }
}

export const setupDatabase = async (): Promise<void> => {
  try {
    // Get database path asynchronously
    dbPath = await getDatabasePath();
    
    // Set the database path in the data source options
    (AppDataSource.options as any).database = dbPath;
    
    // Initialize connection
    await AppDataSource.initialize();

    // Run migrations if any exist
    try {
      const pendingMigrations = await AppDataSource.showMigrations();
      if (pendingMigrations) {
        await AppDataSource.runMigrations();
      }
    } catch (migrationError) {
      console.error('[DB] Error checking/running migrations:', migrationError);
      // Continue despite migration errors
    }
    
    // Create default admin if no users exist
    try {
      await createDefaultAdminUser();
    } catch (adminError) {
      console.error('[DB] Error creating default admin user:', adminError);
      // Continue despite admin creation error
    }
  } catch (error) {
    console.error('[DB] CRITICAL ERROR during database initialization:', error);
    
    // Additional diagnostic information
    if (error instanceof Error) {
      console.error('[DB] Error name:', error.name);
      console.error('[DB] Error message:', error.message);
      console.error('[DB] Error stack:', error.stack);
    }
    
    // Try to check file system access using fs/promises
    try {
      const dbPath = AppDataSource.options.database as string;
      const dbDir = path.dirname(dbPath);
      
      // Use async access to check if directory exists instead of existsSync
      let dirExists = false;
      try {
        await fs.access(dbDir);
        dirExists = true;
        
        // Use async stat instead of statSync
        const stats = await fs.stat(dbDir);
      } catch {
        // Directory doesn't exist or is not accessible
      }
    } catch (fsError) {
      console.error('[DB] Error checking filesystem:', fsError);
    }
    
    throw error;
  }
};